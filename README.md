# 使用前须知

[vue-pure-admin-max](https://github.com/xiaoxian521/vue-pure-admin-max) 仅供购买者（个人、公司）使用且使用者不可售卖或公开源代码，违者追究法律责任、踢出此私有仓库且不退购买费！  
注：若购买者在公司使用了 [vue-pure-admin-max](https://github.com/xiaoxian521/vue-pure-admin-max)，离职后，公司也不可售卖或公开源代码，违者追究其公司法律责任，最高面临`5万元`罚款！

## `vue-pure-admin-max` 与 `vue-pure-admin` 的区别是什么？

答：`vue-pure-admin-max` 在保留 [vue-pure-admin](https://github.com/pure-admin/vue-pure-admin) 所有功能基础上，再添加更多高级功能，每个高级功能仅一个提交记录，方便用户同步代码，具体有哪些高级功能请看 [vue-pure-admin-max 高级功能](https://github.com/pure-admin/vue-pure-admin-max?tab=readme-ov-file#vue-pure-admin-max-%E4%B8%8E-vue-pure-admin-%E7%9A%84%E5%8C%BA%E5%88%AB%E6%98%AF%E5%A6%82%E4%BD%95%E6%8B%A5%E6%9C%89-max-%E7%89%88%E6%9C%AC)  
因为高级功能都是根据提交记录按需取用，所以不会提供精简版！需要哪些功能，请自行根据下面备注的提交记录添加对应代码即可！

## 高级功能提交记录

| **功能**                           | **进度** | 提交记录                                                                                                                                |
| ---------------------------------- | -------- | --------------------------------------------------------------------------------------------------------------------------------------- |
| 新款菜单导航模式（左侧双栏菜单）   | 已完成   | [添加左侧双栏菜单](https://github.com/xiaoxian521/vue-pure-admin-max/commit/d7c3193fcd847f32d77ce96df3e6a8dd7e10adaa)                   |
| 悬浮按钮功能                       | 已完成   | [添加悬浮按钮功能](https://github.com/xiaoxian521/vue-pure-admin-max/commit/1448871cba52044ca379ca1affe355231736b6ad)                   |
| 新增繁體中文、日语、韩语的内置支持 | 已完成   | [新增繁體中文、日语、韩语的内置支持](https://github.com/xiaoxian521/vue-pure-admin-max/commit/c6eadaada65d774d9eb9f0d1108355d83a29c653) |
| 字典管理                           | 已完成   | [添加字典管理](https://github.com/xiaoxian521/vue-pure-admin-max/commit/d25cc87f5f959a5d7ab84ffd55b37104b4a26923)                       |
| 多租户管理                         | 已完成   | [添加多租户管理](https://github.com/xiaoxian521/vue-pure-admin-max/commit/2098e330be672222ce4dd1cc0c769b56479cf550)                     |
| 页头添加整体风格快捷操作按钮       | 已完成   | [页头添加整体风格快捷操作按钮](https://github.com/xiaoxian521/vue-pure-admin-max/commit/a7ea87a1773295970a44726b4231bd3b607c8d01)       |

## 高级功能 `bug` 修复记录

在实际使用中，高级功能难免会出现 `bug`，因此需要进行相应的修复。以下是修复记录

| **高级功能**                     | **修复记录**                                                                                                                                                                |
| -------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 新款菜单导航模式（左侧双栏菜单） | [修复左侧双栏菜单模式下设置路由`showParent: true`，子集菜单未显示的问题](https://github.com/xiaoxian521/vue-pure-admin-max/commit/bad9474f857157cedaec21a155317921d920b250) |
| 字典管理                         | [修复字典管理页面左侧的字典树无法更新的问题](https://github.com/xiaoxian521/vue-pure-admin-max/commit/159aa0b9a127bf15c6c48defc7262da8383205ff)                             |
| 页头添加整体风格快捷操作按钮     | [修复页头添加整体风格快捷操作按钮显示问题](https://github.com/xiaoxian521/vue-pure-admin-max/commit/ee4aa50213d6d25e94ece8a8dd92279c5c324640)                               |

## 温馨提示

当您看到类似 `This repository has been archived` 的提示时，请不要疑惑，这是我将该仓库进行了存档处理，防止大家误提交代码到该仓库。当有新功能或者需要维护时，我会取消存档将功能加上后重新进行存档 🙏
