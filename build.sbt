name := """PlayNebula"""

version := "1.0-SNAPSHOT"

lazy val root = (project in file(".")).enablePlugins(PlayScala)

scalaVersion := "2.13.16"

libraryDependencies ++= Seq(
  guice,
  ws,
  caffeine,
  "org.scalatestplus.play" %% "scalatestplus-play" % "7.0.1" % Test,

  "com.typesafe.play" %% "play-slick" % "5.3.1",
  "com.typesafe.play" %% "play-slick-evolutions" % "5.3.1",
  "mysql" % "mysql-connector-java" % "8.0.33",
  "org.mindrot" % "jbcrypt" % "0.4",

  // JWT依赖
  "com.auth0" % "java-jwt" % "4.5.0",
  //implementation 'com.auth0:auth0:2.19.0'

  // Jackson依赖
  "com.fasterxml.jackson.core" % "jackson-core" % "2.15.3",
  "com.fasterxml.jackson.core" % "jackson-databind" % "2.15.3",
  "com.fasterxml.jackson.module" %% "jackson-module-scala" % "2.15.3",
  "com.fasterxml.jackson.datatype" % "jackson-datatype-jsr310" % "2.15.3", // 支持Java 8日期时间类型

  // commons
  "org.apache.commons" % "commons-lang3" % "3.17.0",
  "commons-io" % "commons-io" % "2.19.0",

  "cn.idev.excel" % "fastexcel" % "1.2.0"


  // Circe 依赖
//  "com.chuusai" %% "shapeless" % "2.3.7",
//  "io.circe" %% "circe-core" % "0.14.4",
//  "io.circe" %% "circe-parser" % "0.14.4",
//  "io.circe" %% "circe-generic" % "0.14.4",
//  "io.circe" %% "circe-generic-extras" % "0.14.4",

  // Monocle dependencies 暂时未使用 拷贝用的
//  "com.github.julien-truffaut" %% "monocle-core" % "3.0.0",
//  "com.github.julien-truffaut" %% "monocle-macro" % "3.0.0"
)

// 自定义任务：启动前端服务
lazy val frontendDir = settingKey[File]("前端项目目录")
frontendDir := baseDirectory.value / "frontend"

lazy val startFrontend = taskKey[Unit]("启动前端服务")
startFrontend := {
  import scala.sys.process._

  // 打印启动前端的消息
  println("\n========================================")
  println("正在启动前端服务...")
  println("========================================\n")

  // 启动前端服务
  val process = Process("pnpm dev", frontendDir.value)
  val io = new ProcessIO(
    _ => (),
    stdout => scala.io.Source.fromInputStream(stdout).getLines.foreach(println),
    stderr => scala.io.Source.fromInputStream(stderr).getLines.foreach(println)
  )
  // process.run(io)

  // 等待一段时间确保进程已启动
  Thread.sleep(2000)

  println("\n========================================")
  println("前端服务启动于 http://localhost:3000")
  println("后端服务启动于 http://localhost:9000")
  println("\n按 Ctrl+C 停止所有服务")
  println("========================================\n")
}

// 添加启动前端的钩子到运行命令
(run / fork) := true
run := {
  (run in Compile).evaluated
  // 自动启动前端服务
  startFrontend.value
}

// Adds additional packages into Twirl
//TwirlKeys.templateImports += "com.example.controllers._"

// Adds additional packages into conf/routes
// play.sbt.routes.RoutesKeys.routesImport += "com.example.binders._"
