package controllers

import core.auth.extractBearerToken
import core.models.ApiResponse
import core.{auth => BearerTokenRegex}
import jakarta.inject._
import models.{RefreshToken, UserCredential, UserLogin}
import play.api.http.{ContentTypes, HeaderNames}
import play.api.libs.json._
import play.api.mvc._
import services.AuthService

import scala.concurrent.{ExecutionContext, Future}

/**
 * 认证控制器
 * 处理用户登录、注销等认证相关请求
 */
class AuthController @Inject()(
                                authService: AuthService,
                                cc: MessagesControllerComponents
                              )(implicit ec: ExecutionContext) extends MessagesAbstractController(cc) {

  /**
   * 处理登录认证
   */
  def authenticate(): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson match {
      case Some(json) =>
        json.validate[UserCredential] match {
          case JsSuccess(credential, _) =>
            authService.authenticate(credential.username, credential.password).map {
              case Some(userLogin) => Ok(ApiResponse.success(userLogin).toJson).as(ContentTypes.JSON)
              case None => Unauthorized(ApiResponse.error[UserLogin](401, "Invalid username or password").toJson)
            }
          case JsError(errors) =>
            Future.successful(BadRequest(ApiResponse.error[UserLogin](400, s"Invalid request format: ${JsError.toJson(errors)}").toJson))
        }
      case None =>
        Future.successful(BadRequest(ApiResponse.error[UserLogin](400, "Expected JSON body").toJson))
    }
  }

  /**
   * 处理用户注销
   */
  def logout(): Action[AnyContent] = Action.async {
    Future.successful(Ok(ApiResponse.success("Successfully logged out").toJson).as(ContentTypes.JSON))
  }

  /**
   * 刷新访问令牌
   * 使用有效的刷新令牌获取新的访问令牌
   */
  def refreshToken(): Action[AnyContent] = Action.async { request =>
    // 从请求中提取刷新令牌
    val refreshTokenOpt = extractBearerToken(request)

    if (refreshTokenOpt.isEmpty) {
      // 从Body中提取刷新令牌
      val refreshToken = request.body.asJson.flatMap(json => (json \ "refreshToken").asOpt[String])

      refreshToken match {
        case Some(token) =>
          authService.refreshUserToken(token).map {
            case Some(refreshedToken) =>
              Ok(ApiResponse.success(refreshedToken).toJson).as(ContentTypes.JSON)
            case None =>
              Unauthorized(ApiResponse.error[RefreshToken](401, "Invalid or expired refresh token").toJson)
          }
        case None => Future.successful(BadRequest(ApiResponse.error[RefreshToken](400, "Refresh token is required").toJson))
      }

    } else {
      Future.successful(BadRequest(ApiResponse.error[RefreshToken](400, "Refresh token is required").toJson))
    }

  }
}