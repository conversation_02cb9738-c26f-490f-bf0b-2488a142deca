package filters

import core.auth.extractBearerToken
import jakarta.inject.Inject
import org.apache.pekko.stream.Materializer
import play.api.mvc.Results.NotFound
import play.api.mvc._
import uitls.JwtUtil

import scala.concurrent.{ExecutionContext, Future}

/**
 * 身份验证过滤器
 * 验证请求中的JWT令牌，保护需要身份验证的API端点
 *
 * @param mat     物化器
 * @param ec      执行上下文
 * @param jwtUtil JWT工具类
 */
class AuthFilter @Inject()(
                            implicit val mat: Materializer,
                            ec: ExecutionContext,
                            jwtUtil: JwtUtil
                          ) extends Filter {

  // 不需要身份验证的路径
  private val publicPaths = Seq(
    "/api/auth/login",
    "/api/auth/refresh",
    "/api/auth/logout",
    "/assets/",
    "/favicon.ico",
    "/file",
    "/"
  )

  /**
   * 应用过滤器
   *
   * @param nextFilter    下一个要应用的过滤器
   * @param requestHeader 请求头
   * @return 带有结果的Future
   */
  override def apply(nextFilter: RequestHeader => Future[Result])
                    (requestHeader: RequestHeader): Future[Result] = {

    // 检查是否是公共路径，如果是则不需要验证
    if (isPublicPath(requestHeader.path)) {
      nextFilter(requestHeader)
    } else {
      // 提取Authorization头
      extractBearerToken(requestHeader) match {
        case Some(token) =>
          // 验证令牌
          jwtUtil.verifyToken(token) match {
            case Some(_) =>
              // 令牌有效，继续处理请求
              nextFilter(requestHeader)

            case None =>
              // 令牌无效，返回401 Unauthorized
              val msg = Some("""{"code":401,"success":false,"message":"Invalid or expired token","data":null,"errors":null}""")
              Future.successful(
                NotFound(views.html.index(None))
              )
          }

        case None =>
          // 没有提供令牌，返回401 Unauthorized
          val msg = Some("""{"code":401,"success":false,"message":"Authentication required","data":null,"errors":null}""")
          Future.successful(
            NotFound(views.html.index(None))
          )
      }
    }
  }

  /**
   * 检查路径是否是公共路径（不需要验证）
   *
   * @param path 请求路径
   * @return 是否是公共路径
   */
  private def isPublicPath(path: String): Boolean = {
    publicPaths.exists(p => path.startsWith(p) || path == p)
  }

}
