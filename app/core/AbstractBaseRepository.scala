package core

import core.models.BaseEntity
import jakarta.inject.Inject
import play.api.db.slick.DatabaseConfigProvider

import scala.concurrent.ExecutionContext


/**
 * Abstract base repository implementation that can be extended
 *
 * @param dbConfigProvider Database configuration provider
 * @param ec               Execution context
 * @tparam E Entity type that extends BaseEntity
 * @tparam T Table type that extends BaseTable
 */
abstract class AbstractBaseRepository[E <: BaseEntity, T <: BaseTable[E]] @Inject()
(protected val dbConfigProvider: DatabaseConfigProvider)
(implicit val ec: ExecutionContext) extends BaseRepository[E, T]
