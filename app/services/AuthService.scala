package services

import models.{RefreshToken, UserLogin}
import modules.org.models.User
import uitls.RSAUtil

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.concurrent.Future

/**
 * 认证服务接口
 */
trait AuthService {
  /**
   * 验证用户身份
   *
   * @param username 用户名
   * @param password 已加密的密码
   * @return 如果验证成功则返回Some(User)，否则返回None
   */
  def authenticate(username: String, password: String): Future[Option[UserLogin]]

  /**
   * 通过用户名获取用户信息
   *
   * @param username 用户名
   * @return 用户信息
   */
  def getUserByUsername(username: String): Future[Option[User]]

  /**
   * 刷新令牌
   *
   * @param refreshToken 刷新令牌
   * @return 如果刷新成功则返回Some(RefreshToken)，否则返回None
   */
  def refreshUserToken(refreshToken: String): Future[Option[RefreshToken]]
}

object AuthService {

  /**
   * 创建用户加密密码
   * 使用RSA加密密码+盐的组合
   *
   * @param password 原始密码
   * @param salt     盐值
   * @return 加密后的密码
   */
  def createUserPassword(password: String, salt: String): String = {
    try {
      // 将密码和盐组合在一起
      val saltedPassword = password + salt
      // 使用RSA公钥加密
      RSAUtil.encrypt(saltedPassword, RSAUtil.PUBLIC_KEY)
    } catch {
      case e: Exception =>
        // 如果加密过程出错，返回Base64编码的原始密码（不推荐用于生产环境）
        Base64.getEncoder.encodeToString(password.getBytes(StandardCharsets.UTF_8))
    }
  }

  /**
   * 验证用户密码
   * 解密存储的密码并与输入的密码+盐组合进行比较
   *
   * @param inputPassword  输入的密码
   * @param storedPassword 存储的加密密码
   * @param salt           盐值
   * @return 密码是否匹配
   */
  def verifyUserPassword(inputPassword: String, storedPassword: String, salt: String): Boolean = {
    try {
      // 解密存储的密码
      val decryptedPassword = RSAUtil.decrypt(storedPassword, RSAUtil.PRIVATE_KEY)
      // 验证输入的密码+盐是否与解密后的值匹配
      decryptedPassword == (inputPassword + salt)
    } catch {
      case e: Exception =>
        // 如果解密过程出错，尝试比较Base64编码的输入密码与存储密码
        val encodedInput = Base64.getEncoder.encodeToString(inputPassword.getBytes(StandardCharsets.UTF_8))
        encodedInput == storedPassword
    }
  }
}