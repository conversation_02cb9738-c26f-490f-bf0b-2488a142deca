package services

import jakarta.inject.{Inject, Singleton}
import models.{RefreshToken, UserLogin}
import modules.authority.service.AuthorityService
import modules.org.models.User
import modules.org.service.OrganizationService
import uitls.{JwtUtil, RSAUtil}

import java.time.OffsetDateTime
import scala.concurrent.{ExecutionContext, Future}


/**
 * 认证服务实现
 */
@Singleton
class AuthServiceImpl @Inject()(
                                 orgService: OrganizationService,
                                 authorityService: AuthorityService,
                                 jwtUtil: JwtUtil
                               )(implicit ec: ExecutionContext) extends AuthService {
  /**
   * 验证用户身份（加密密码）
   *
   * @param username 用户名
   * @param password 已加密的密码
   * @return 如果验证成功则返回Some(User)，否则返回None
   */
  override def authenticate(username: String, password: String): Future[Option[UserLogin]] = {
    orgService.findUserByUsername(username).flatMap { userOpt =>
      userOpt.map { user =>
        val decryptedPassword = RSAUtil.decrypt(password, RSAUtil.PRIVATE_KEY)
        val passwordMatch = AuthService.verifyUserPassword(decryptedPassword, user.password, user.username)

        if (passwordMatch && user.status.getOrElse(0) == 1) {
          val userId = user.id.getOrElse("")

          // 获取用户角色和权限
          val rolesFuture = authorityService.getUserRoleCodes(userId)
          val permissionsFuture = authorityService.getUserPermissionCodes(userId)

          // 合并结果
          for {
            roles <- rolesFuture
            permissions <- permissionsFuture
          } yield {
            // 生成JWT令牌
            val accessToken = jwtUtil.generateAccessToken(userId, user.username)
            val refreshToken = jwtUtil.generateRefreshToken(userId, user.username)
            val expires = jwtUtil.getExpirationDate(accessToken).getOrElse(OffsetDateTime.now().plusMinutes(30))

            // 返回的登录信息
            Some(UserLogin(
              avatar = user.avatar.getOrElse(""),
              username = user.username,
              nickname = user.nickname.getOrElse("星云"),
              roles = roles,
              permissions = permissions,
              accessToken = accessToken,
              refreshToken = refreshToken,
              expires = expires
            ))
          }
        } else {
          Future.successful(None)
        }
      }.getOrElse(Future.successful(None))
    }
  }

  /**
   * 通过用户名获取用户信息
   *
   * @param username 用户名
   * @return 用户信息
   */
  override def getUserByUsername(username: String): Future[Option[User]] = {
    orgService.findUserByUsername(username).map { userOpt =>
      userOpt.map { user =>
        User(
          id = user.id,
          avatar = user.avatar,
          username = user.username,
          nickname = user.nickname,
          sex = user.sex,
          email = user.email,
          phone = user.phone,
          password = user.password,
          description = user.description,
          status = user.status,
          createdAt = user.createdAt,
          updatedAt = user.updatedAt,
          deleted = user.deleted
        )
      }
    }
  }

  /**
   * 刷新令牌
   *
   * @param refreshToken 刷新令牌
   * @return 如果刷新成功则返回Some(RefreshToken)，否则返回None
   */
  override def refreshUserToken(refreshToken: String): Future[Option[RefreshToken]] = {
    Future.successful {
      if (jwtUtil.isRefreshToken(refreshToken)) {
        for {
          userId <- jwtUtil.getUserIdFromToken(refreshToken)
          username <- jwtUtil.getUsernameFromToken(refreshToken)
        } yield {
          val newAccessToken = jwtUtil.generateAccessToken(userId, username)
          val expires = jwtUtil.getExpirationDate(newAccessToken).getOrElse(OffsetDateTime.now().plusMinutes(30))

          RefreshToken(
            accessToken = newAccessToken,
            refreshToken = refreshToken, // 保持相同的刷新令牌
            expires = expires
          )
        }
      } else {
        None
      }
    }
  }
}