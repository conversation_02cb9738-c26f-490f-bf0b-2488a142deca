@*
* This template is called from the `index` template. This template
* handles the rendering of the page header and body tags. It takes
* two arguments, a `String` for the title of the page and an `Html`
* object to insert into the body of the page.
*@
@(title: String)(content: Html)

<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        @* Here's where we render the page title `String`. *@
        <title>@title</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
        <link rel="shortcut icon" type="image/png" href="@routes.Assets.versioned("images/favicon.png")">
        <script>
                tailwind.config = {
                    theme: {
                        extend: {
                            colors: {
                                primary: {
                                    50: '#f5f3ff',
                                    100: '#ede9fe',
                                    200: '#ddd6fe',
                                    300: '#c4b5fd',
                                    400: '#a78bfa',
                                    500: '#8b5cf6',
                                    600: '#7c3aed',
                                    700: '#6d28d9',
                                    800: '#5b21b6',
                                    900: '#4c1d95',
                                }
                            }
                        }
                    }
                }
        </script>
    </head>
    <body class="bg-gray-50 text-gray-800">
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="#" class="text-primary-600 text-xl font-bold">
                            <i class="fas fa-cloud-bolt mr-2"></i>PlayNebula
                        </a>
                    </div>
                    <div class="hidden md:flex items-center space-x-6">
                        <a href="#features" class="text-gray-700 hover:text-primary-600 transition">特性</a>
                        <a href="#docs" class="text-gray-700 hover:text-primary-600 transition">文档</a>
                        <a href="#examples" class="text-gray-700 hover:text-primary-600 transition">示例</a>
                        <a href="https://github.com/treedeep" target="_blank" class="text-gray-700 hover:text-primary-600 transition">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        @* And here's where we render the `Html` object containing
        * the page content. *@
        @content

        <footer class="bg-gray-800 text-white py-8 mt-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div>
                        <h3 class="text-lg font-bold mb-2">PlayNebula</h3>
                        <p class="text-gray-300 text-sm">简单、高效的开发框架</p>
                    </div>
                    <div class="mt-4 md:mt-0 flex space-x-4">
                        <a href="https://github.com/playframework/playframework" class="text-gray-300 hover:text-white transition">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://twitter.com/playframework" class="text-gray-300 hover:text-white transition">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://discord.com/invite/g5s2vtZ4Fa" class="text-gray-300 hover:text-white transition">
                            <i class="fab fa-discord text-xl"></i>
                        </a>
                    </div>
                </div>
                <div class="mt-6 text-center text-gray-400 text-sm">
                    © @{new java.text.SimpleDateFormat("yyyy").format(new java.util.Date())} PlayNebula. 保留所有权利。
                </div>
            </div>
        </footer>
    </body>
</html>
