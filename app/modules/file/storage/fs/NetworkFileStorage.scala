package modules.file.storage.fs

import jakarta.inject.Inject
import modules.file.storage.FileStorage
import modules.file.Constant.networkFilePrefix
import modules.file.models.FileRef
import org.apache.pekko.util.ByteString
import play.api.libs.ws.WSClient

import java.io.InputStream
import scala.collection.immutable.{Map => ImmutableMap}
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

/** Copyright © 深圳市树深计算机系统有限公司 版权所有
  * <p>
  * 网络文件存储（仅访问）
  *
  * <AUTHOR>
  *         v1 2024/7/30 22:32
  */
class   NetworkFileStorage @Inject() (
    ws: WSClient
) extends FileStorage {

  def getStorageName: String = networkFilePrefix

  override def saveStream(
      fileName: String,
      inputStream: InputStream,
      parameters: ImmutableMap[String, String]
  ): Option[FileRef] = None

  override def openStream(fileRef: FileRef): Future[InputStream] = {
    downloadFile(fileRef.path)
  }

  override def removeFile(fileRef: FileRef): Unit = {}

  override def fileExists(fileRef: FileRef): Boolean = true

  /** Downloads a file from the given URL and converts it to InputStream.
    *
    * @param url The URL of the file to download.
    * @return Future containing InputStream of the downloaded file.
    */
  private def downloadFile(url: String): Future[InputStream] = {
    ws.url(url).get().map { response =>
      if (response.status == 200) {
        val byteString: ByteString = response.bodyAsBytes
        byteString.iterator.asInputStream
      } else {
        throw new RuntimeException(
          s"Failed to download file from $url, status: ${response.status}"
        )
      }
    }
  }

}
