package modules.file.storage.fs

import jakarta.inject.Inject
import modules.file.models.FileRef
import modules.file.storage.FileStorage
import play.api.inject.ApplicationLifecycle
import play.api.libs.ws.WSClient

import java.io.InputStream
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

/** Copyright © 深圳市树深计算机系统有限公司 版权所有
  * <p>
  * Tencent COS 文件存储
  *
  * <AUTHOR>
  * v1 2024/7/30 22:32
  */
class TencentFileStorage @Inject() (
    ws: WSClient,
    lifecycle: ApplicationLifecycle
) extends FileStorage {

  def getStorageName: String = "tencent"

  def saveStream(
      fileName: String,
      inputStream: InputStream,
      parameters: Map[String, String]
  ): Option[FileRef] = {
    Some(FileRef(getStorageName, "", fileName, parameters))
  }

  def openStream(reference: FileRef): Future[InputStream] = {
    Future.failed(new Exception())
  }

  def removeFile(reference: FileRef): Unit = {}

  def fileExists(reference: FileRef): Boolean = { false }

  // Registering the shutdown hook
  lifecycle.addStopHook { () =>
    Future {}
  }
}
