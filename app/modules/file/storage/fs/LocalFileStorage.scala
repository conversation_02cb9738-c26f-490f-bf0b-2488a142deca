package modules.file.storage.fs

import jakarta.inject.Inject
import modules.file.models.FileRef
import modules.file.storage.FileStorageException.Type
import modules.file.storage.{FileStorage, FileStorageException}
import org.apache.commons.io.{FileUtils, FilenameUtils, IOUtils}
import org.apache.commons.lang3.StringUtils
import org.slf4j.{Logger, LoggerFactory}
import play.api.Environment
import play.api.inject.ApplicationLifecycle
import play.api.libs.ws.WSClient

import java.io.{IOException, InputStream}
import java.nio.file.{Files, Path, Paths, StandardOpenOption}
import java.util.concurrent.{ExecutorService, Executors}
import java.util.{Calendar, UUID}
import scala.collection.immutable.{Map => ImmutableMap}
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

/** Copyright © 深圳市树深计算机系统有限公司 版权所有
 * <p>
 * 本地文件存储
 *
 * <AUTHOR>
 *         v1 2024/7/30 22:32
 */
class LocalFileStorage @Inject()(
                                  ws: WSClient,
                                  lifecycle: ApplicationLifecycle,
                                  environment: Environment
                                ) extends FileStorage {
  private val log: Logger = LoggerFactory.getLogger(classOf[LocalFileStorage])

  private val workDir: Path =
    Paths.get(environment.rootPath.getAbsolutePath, ".nebula")
  private var storageRoots: Array[Path] = _
  private val writeExecutor: ExecutorService = Executors.newFixedThreadPool(5)
  private val isImmutableFileStorage: Boolean = false

  // Ensure the workDir exists
  if (!Files.exists(workDir)) {
    Files.createDirectories(workDir)
  }

  def getStorageName: String = "fs"

  private def createUuidFilename(fileName: String): String = {
    val extension = FilenameUtils.getExtension(fileName)
    if (StringUtils.isNotEmpty(extension)) {
      s"${UUID.randomUUID}.$extension"
    } else {
      UUID.randomUUID.toString
    }
  }

  private def getStorageRoots: Array[Path] = {
    if (storageRoots == null) {
      val dir = workDir.resolve("filestorage")
      if (!Files.exists(dir) && !Files.createDirectories(dir).toFile.exists) {
        throw new FileStorageException(
          Type.IOException,
          s"Cannot create filestorage directory: ${dir.toAbsolutePath}"
        )
      }
      storageRoots = Array(dir)
    }
    storageRoots
  }

  override def saveStream(
                           fileName: String,
                           inputStream: InputStream,
                           parameters: ImmutableMap[String, String]
                         ): Option[FileRef] = {
    val relativePath = createRelativeFilePath(fileName)
    val fileRef =
      new FileRef(
        getStorageName,
        pathToString(relativePath),
        fileName,
        parameters
      )
    saveStream(fileRef, inputStream)
    Some(fileRef)
  }

  override def openStream(fileRef: FileRef): Future[InputStream] = {
    val relativePath = getRelativePath(fileRef.getPath)
    val roots = getStorageRoots

    if (roots.isEmpty) {
      log.error("No storage directories available")
      Future.failed(
        new FileStorageException(Type.IOException, fileRef.toString)
      )
    } else {
      val maybeStream = roots.collectFirst {
        case root if Files.exists(root.resolve(relativePath)) =>
          Try(Files.newInputStream(root.resolve(relativePath)))
      }

      maybeStream match {
        case Some(Success(inputStream)) => Future.successful(inputStream)
        case Some(Failure(exception)) => Future.failed(exception)
        case None =>
          Future.failed(
            new FileStorageException(Type.IOException, fileRef.toString)
          )
      }
    }
  }

  override def removeFile(fileRef: FileRef): Unit = {
    val relativePath = getRelativePath(fileRef.getPath)
    getStorageRoots.foreach { root =>
      val filePath = root.resolve(relativePath)
      if (Files.exists(filePath) && !Files.deleteIfExists(filePath)) {
        throw new FileStorageException(
          Type.IOException,
          s"Unable to delete file ${filePath.toAbsolutePath}"
        )
      }
    }
  }

  override def fileExists(fileRef: FileRef): Boolean = {
    val relativePath = getRelativePath(fileRef.getPath)
    getStorageRoots.exists(root => Files.exists(root.resolve(relativePath)))
  }

  private def createRelativeFilePath(fileName: String): Path =
    createDateDirPath().resolve(createUuidFilename(fileName))

  private def createDateDirPath(): Path = {
    val cal = Calendar.getInstance()
    Paths.get(
      cal.get(Calendar.YEAR).toString,
      StringUtils.leftPad((cal.get(Calendar.MONTH) + 1).toString, 2, '0'),
      StringUtils.leftPad(cal.get(Calendar.DAY_OF_MONTH).toString, 2, '0')
    )
  }

  private def checkFileExists(path: Path): Unit = {
    if (Files.exists(path) && isImmutableFileStorage) {
      throw new FileStorageException(
        Type.FileAlreadyExists,
        path.toAbsolutePath.toString
      )
    }
  }

  private def checkStorageAccessible(
                                      roots: Array[Path],
                                      fileName: String
                                    ): Unit = {
    if (!roots.head.toFile.exists && !roots.head.toFile.mkdirs()) {
      log.error(s"Inaccessible primary storage at ${roots.head}")
      throw new FileStorageException(Type.StorageInaccessible, fileName)
    }
  }

  private def checkStorageDefined(
                                   roots: Array[Path],
                                   fileName: String
                                 ): Unit = {
    if (roots.isEmpty) {
      log.error("No storage directories defined")
      throw new FileStorageException(Type.StorageInaccessible, fileName)
    }
  }

  private def getRelativePath(path: String): Path = {
    val parts = path.split("/", 4)
    if (parts.length < 4) throw new IllegalArgumentException("Invalid path")
    Paths.get(parts(0), parts(1), parts(2), parts(3))
  }

  private def pathToString(path: Path): String =
    path.toString.replace('\\', '/')

  private def saveStream(fileRef: FileRef, inputStream: InputStream): Long = {
    val relativePath = getRelativePath(fileRef.getPath)
    val roots = getStorageRoots
    checkStorageDefined(roots, fileRef.getFileName)
    checkStorageAccessible(roots, fileRef.getFileName)
    val path = roots.head.resolve(relativePath)
    val parentPath = Option(path.getParent).getOrElse(
      throw new FileStorageException(
        Type.IOException,
        s"Invalid storage root: $path"
      )
    )

    if (!Files.exists(parentPath) && !parentPath.toFile.mkdirs()) {
      throw new FileStorageException(
        Type.IOException,
        s"Cannot create directory: ${parentPath.toAbsolutePath}"
      )
    }

    checkFileExists(path)

    var size: Long = 0L
    try {
      val outputStream =
        Files.newOutputStream(path, StandardOpenOption.CREATE_NEW)
      try {
        size = IOUtils.copyLarge(inputStream, outputStream)
        outputStream.flush()
      } catch {
        case e: Throwable => throw e
      } finally {
        outputStream.close()
      }
    } catch {
      case e: IOException =>
        FileUtils.deleteQuietly(path.toFile)
        throw new FileStorageException(
          Type.IOException,
          path.toAbsolutePath.toString,
          e
        )
    }

    for (i <- 1 until roots.length) {
      if (!roots(i).toFile.exists()) {
        log.error(
          s"Error saving ${fileRef.getFileName} into ${roots(i)} : directory doesn't exist"
        )
      } else {
        val pathCopy = roots(i).resolve(relativePath)
        writeExecutor.submit(new Runnable {
          override def run(): Unit = {
            try {
              FileUtils.copyFile(path.toFile, pathCopy.toFile, true)
            } catch {
              case e: Exception =>
                log.error(
                  s"Error saving ${fileRef.getFileName} into $pathCopy : ${e.getMessage}"
                )
            }
          }
        })
      }
    }
    size
  }

  // Registering the shutdown hook
  lifecycle.addStopHook { () =>
    Future {
      writeExecutor.shutdown()
    }
  }
}
