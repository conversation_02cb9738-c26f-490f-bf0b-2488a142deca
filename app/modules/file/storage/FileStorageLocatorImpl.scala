package modules.file.storage

import jakarta.inject._
import modules.file.Constant.defaultFileStorageName

import java.util.{Set => jSet}
import scala.jdk.CollectionConverters._

class FileStorageLocatorImpl @Inject() (storages: jSet[FileStorage])
    extends FileStorageLocator {

  private val storageMap: Map[String, FileStorage] = storages.asScala.map {
    storage =>
      storage.getStorageName -> storage
  }.toMap

  def getByName[T <: FileStorage](storageName: String): T = {
    storageMap.get(storageName) match {
      case None =>
        throw new IllegalArgumentException(
          s"FileStorage not found: $storageName"
        )
      case Some(fileStorage) => fileStorage.asInstanceOf[T]
    }
  }

  def getDefault[T <: FileStorage]: T = {

    (Option(defaultFileStorageName).flatMap(storageMap.get) orElse {
      if (storages.size == 1) Some(storages.asScala.head) else None
    }).map(_.asInstanceOf[T]).getOrElse {
      if (storages.isEmpty) {
        throw new IllegalStateException("No FileStorage beans registered")
      } else {
        throw new IllegalStateException(
          "There are more than one FileStorage beans registered"
        )
      }
    }
  }
}
