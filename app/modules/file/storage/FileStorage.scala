package modules.file.storage

import modules.file.models.FileRef

import java.io.InputStream
import scala.collection.immutable.{Map => ImmutableMap}
import scala.concurrent.Future

trait FileStorage {
  def getStorageName: String

  def saveStream(fileName: String, inputStream: InputStream): Option[FileRef] = {
    saveStream(fileName, inputStream, Map.empty[String, String])
  }

  def saveStream(
      fileName: String,
      inputStream: InputStream,
      parameters: ImmutableMap[String, String]
  ): Option[FileRef]

  def openStream(reference: FileRef): Future[InputStream]

  def removeFile(reference: FileRef): Unit

  def fileExists(reference: FileRef): Boolean
}
