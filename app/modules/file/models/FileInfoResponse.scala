package modules.file.models

import modules.file.Constant.downloadUrlPrefix


case class FileInfoResponse(
    fileRef: String,
    name: String,
    contentType: String,
    size: Long,
    url: Option[String] = None
)

object FileInfoResponse {
  def apply(
      fileRef: String,
      name: String,
      contentType: String,
      size: Long,
      url: Option[String] = None
  ): FileInfoResponse = {

    if (url.isDefined) {
      new FileInfoResponse(
        fileRef,
        name,
        contentType,
        size,
        url
      )
    } else {
      new FileInfoResponse(
        fileRef,
        name,
        contentType,
        size,
        Some(s"$downloadUrlPrefix$fileRef")
      )
    }

  }

}
