package modules.file.models

import modules.file.uitls.FileTypesHelper
import modules.file.Constant.{downloadUrlPrefix, http, networkFilePrefix}
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang3.StringUtils
import uitls.URLEncodeUtils

import java.net.{URI, URISyntaxException}
import java.util.Objects
import scala.collection.immutable.{Map => ImmutableMap}

case class FileRef(
                    storageName: String,
                    path: String,
                    fileName: String,
                    parameters: ImmutableMap[String, String] = ImmutableMap.empty
                  ) extends Serializable {

  def getStorageName: String = storageName

  def getPath: String = path

  def getFileName: String = fileName

  def getParameters: ImmutableMap[String, String] = parameters

  def getUrl: String = {
    if (getStorageName == networkFilePrefix) {
      getPath
    }
    else {
      s"$downloadUrlPrefix$toString"
    }
  }

  def addParameter(key: String, value: String): FileRef = {
    copy(parameters = parameters + (key -> value))
  }

  override def toString: String = {
    if (storageName == networkFilePrefix) {
      return path
    }

    val uriStringBuilder = new StringBuilder
    uriStringBuilder
      .append(storageName)
      .append("://")
      .append(path)
      .append("?name=")
      .append(URLEncodeUtils.encodeUtf8(fileName))
      .append("&")
      .append("contentType")
      .append("=")
      .append(URLEncodeUtils.encodeUtf8(getContentType))
    parameters.foreach { case (key, value) =>
      uriStringBuilder
        .append("&")
        .append(key)
        .append("=")
        .append(URLEncodeUtils.encodeUtf8(value))
    }
    uriStringBuilder.toString()
  }

  def getContentType: String = FileRef.getContentType(getFileName)

  override def equals(o: Any): Boolean = o match {
    case that: FileRef =>
      storageName == that.storageName &&
        path == that.path
    case _ => false
  }

  override def hashCode(): Int = {
    Objects.hash(storageName, path)
  }
}

object FileRef {

  def getContentType(fileName: String): String = {
    val extension = FilenameUtils.getExtension(fileName)
    if (StringUtils.isEmpty(extension)) "application/octet-stream"
    else FileTypesHelper.getMIMEType(s".$extension".toLowerCase)
  }

  def create(storageName: String, path: String, fileName: String): FileRef = {
    new FileRef(storageName, path, fileName)
  }

  def fromString(fileRefString: String): FileRef = {

    if (fileRefString.startsWith(http)) {
      return new FileRef(networkFilePrefix, fileRefString, null)
    }

    if (fileRefString.startsWith(networkFilePrefix)) {
      val url = fileRefString.substring(6)
      val index = url.lastIndexOf("fileName=")
      var fileName = url.substring(index + 9)

      if (index == -1) {
        fileName = "noname.file"
      }

      val encodeName = URLEncodeUtils.decodeUtf8(fileName)

      return new FileRef(networkFilePrefix, url, encodeName)
    }

    val fileRefUri =
      try {
        new URI(fileRefString)
      } catch {
        case e: URISyntaxException =>
          throw new IllegalArgumentException(
            s"Cannot convert $fileRefString to FileRef",
            e
          )
      }

    val storageName = fileRefUri.getScheme
    val path =
      Option(fileRefUri.getAuthority).getOrElse("") + fileRefUri.getPath
    val query = fileRefUri.getRawQuery

    if (StringUtils.isAnyBlank(storageName, path, query)) {
      throw new IllegalArgumentException(
        s"Cannot convert $fileRefString to FileRef"
      )
    } else {
      val params = query.split("&")
      val nameParamPair = params(0).split("=", -1)
      if (nameParamPair.length != 2) {
        throw new IllegalArgumentException(
          s"Cannot convert $fileRefString to FileRef"
        )
      } else {
        val fileName = URLEncodeUtils.decodeUtf8(nameParamPair(1))
        if (params.length > 1) {
          val paramsMap = params
            .drop(1)
            .map { param =>
              val paramPair = param.split("=", -1)
              if (paramPair.length != 2) {
                throw new IllegalArgumentException(
                  s"Cannot convert $fileRefString to FileRef"
                )
              }
              paramPair(0) -> URLEncodeUtils.decodeUtf8(paramPair(1))
            }
            .toMap
          new FileRef(storageName, path, fileName, paramsMap)
        } else {
          new FileRef(storageName, path, fileName)
        }
      }
    }
  }
}
