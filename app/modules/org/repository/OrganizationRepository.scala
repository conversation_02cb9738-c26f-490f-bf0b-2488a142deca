package modules.org.repository

import core.utils.Snowflake
import jakarta.inject.{Inject, Singleton}
import modules.org.models.{Department, User}
import modules.org.models.{DepartmentTable, PositionTable, UserPositionTable, UserTable}
import play.api.db.slick.{DatabaseConfigProvider, HasDatabaseConfigProvider}
import slick.jdbc.JdbcProfile
import slick.jdbc.MySQLProfile.api._
import slick.lifted.TableQuery

import java.time.OffsetDateTime
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.{ExecutionContext, Future}


@Singleton
class OrganizationRepository @Inject()(val dbConfigProvider: DatabaseConfigProvider,
                                       val ec: ExecutionContext)
  extends HasDatabaseConfigProvider[JdbcProfile] {

  private val usersTableQuery = TableQuery[UserTable]
  private val positionsTableQuery = TableQuery[PositionTable]
  private val userPositionsTableQuery = TableQuery[UserPositionTable]
  private val departmentsTableQuery = TableQuery[DepartmentTable]


  def findUserByUsername(username: String): Future[Option[User]] = {
    db.run(
      usersTableQuery
        .filter(_.username === username)
        .filter(_.deleted === false)
        .result
        .headOption
    )
  }

  def findUserById(userId: String): Future[Option[User]] = {
    db.run(
      usersTableQuery
        .filter(_.id === userId)
        .filter(_.deleted === false)
        .result
        .headOption
    )
  }

  def deptList(): Future[Seq[Department]] = {
    db.run(
      departmentsTableQuery
        .filter(_.deleted === false)
        .result
    )
  }

  def findDeptNameById(id: String): Future[Option[String]] = {
    db.run(
      departmentsTableQuery
        .filter(_.id === id)
        .filter(_.deleted === false)
        .map(_.name)
        .result
        .headOption
    )
  }

  def findDeptById(id: String): Future[Option[Department]] = {
    db.run(
      departmentsTableQuery
        .filter(_.id === id)
        .filter(_.deleted === false)
        .result
        .headOption
    )
  }

  def addDept(entity: Department): Future[Option[String]] = {
    entity.id = Some(Snowflake.nextIdStr())
    db.run(departmentsTableQuery += entity).map(_ => entity.id)
  }

  def updateDept(entity: Department): Future[Int] = {
    entity.id match {
      case Some(id) =>
        db.run(
          departmentsTableQuery
            .filter(_.id === id)
            .filter(_.deleted === false)
            .update(entity)
        )
      case None => Future.successful(0)
    }
  }

  def deleteDept(id: String): Future[Int] = {
    db.run(
      departmentsTableQuery
        .filter(_.id === id)
        .delete
    )
  }

  def userList(): Future[Seq[User]] = {
    db.run(
      usersTableQuery
        .filter(_.deleted === false)
        .result
    )
  }

  def userPage(page: Int, pageSize: Int, search: User.Search): Future[(Seq[User], Int)] = {
    val offset = (page - 1) * pageSize

    // Create a base query that applies the search filters
    val baseQuery = usersTableQuery
      .filter(_.deleted === false)
      // Apply search filters separately
      .filterIf(search.deptId.isDefined)(u => u.deptId.getOrElse("") === search.deptId.get)
      .filterIf(!search.phone.isBlank)(u => u.phone.getOrElse("") like s"%${search.phone}%")
      .filterIf(search.status.isDefined)(u => u.status.getOrElse(0L) === search.status.get)
      .filterIf(!search.username.isBlank)(u => u.username like s"%${search.username}%")

    // Query for total count (for pagination)
    val totalQuery = baseQuery.length.result

    // Query for data with pagination
    val dataQuery = baseQuery
      .drop(offset)
      .take(pageSize)
      .result

    // Run both queries in parallel and combine results
    db.run(dataQuery zip totalQuery)
  }

  /**
   * 添加用户
   *
   * @param entity 用户实体
   * @return 如果添加成功则返回用户ID，否则返回None
   */
  def addUser(entity: User): Future[Option[String]] = {
    entity.id = Some(Snowflake.nextIdStr())
    db.run(usersTableQuery += entity).map(_ => entity.id)
  }

  /**
   * 更新用户
   *
   * @param entity 用户实体
   * @return 更新的记录数
   */
  def updateUser(entity: User): Future[Int] = {
    entity.id match {
      case Some(id) =>
        db.run(
          usersTableQuery
            .filter(_.id === id)
            .filter(_.deleted === false)
            .update(entity)
        )
      case None => Future.successful(0)
    }
  }

  /**
   * 删除用户
   *
   * @param id 用户ID
   * @return 删除的记录数
   */
  def deleteUser(id: String): Future[Int] = {
    db.run(
      usersTableQuery
        .filter(_.id === id)
        .map(u => (u.deleted, u.updatedAt))
        .update((true, OffsetDateTime.now()))
    )
  }

  def deleteUsers(ids: Seq[String]): Future[Int] = {
    if (ids.isEmpty) {
      Future.successful(0)
    } else {
      val now = OffsetDateTime.now()
      db.run(
        usersTableQuery
          .filter(_.id inSet ids)
          .filter(_.deleted === false)
          .map(e => (e.deleted, e.updatedAt))
          .update((true, now))
      )
    }
  }

  def updateUserAvatar(userId: String, fileRef: String): Future[Int] = {
    db.run(
      usersTableQuery
        .filter(_.id === userId)
        .map(u => (u.avatar, u.updatedAt))
        .update((Some(fileRef), OffsetDateTime.now()))
    )
  }

}