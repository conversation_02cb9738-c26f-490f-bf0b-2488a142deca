package modules.org.controller

import core.auth.{SecuredAction, SecuredInfoAction}
import core.models.ApiResponse
import core.utils.JsonUtils
import jakarta.inject.{Inject, Singleton}
import modules.authority.models.Role
import modules.file.Constant.downloadUrlPrefix
import modules.file.service.FileTransferService
import modules.org.models.{Department, User}
import modules.org.service.OrganizationService
import org.apache.pekko.stream.Materializer
import org.slf4j.{Logger, LoggerFactory}
import play.api.http.ContentTypes
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

/**
 * 组织架构控制器
 * 提供组织架构相关的API接口
 *
 * @param service           组织架构服务
 * @param securedAction     安全动作
 * @param securedInfoAction 安全信息动作
 * @param cc                控制器组件
 * @param ec                执行上下文
 */
@Singleton
class OrganizationController @Inject()(
                                        mat: Materializer,
                                        service: OrganizationService,
                                        securedAction: SecuredAction,
                                        securedInfoAction: SecuredInfoAction,
                                        cc: ControllerComponents,
                                        fileTransferService: FileTransferService
                                      ) extends AbstractController(cc) {

  private val log: Logger = LoggerFactory.getLogger(classOf[OrganizationController])

  /**
   * 获取当前登录用户信息
   * 使用SecuredInfoAction自动处理认证和用户信息获取
   *
   * @return 当前用户信息
   */
  def userInfo(): Action[AnyContent] = securedInfoAction { request =>
    // 已经通过SecuredInfoAction认证并获取了用户信息
    Ok(ApiResponse.success(request.userInfo).toJson).as(ContentTypes.JSON)
  }

  def deptList(): Action[AnyContent] = Action.async {
    service.deptList().map(list => Ok(ApiResponse.success(list).toJson).as(ContentTypes.JSON))
  }

  def deptAdd(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, classOf[Department])
      service.addDept(entity).map {
        case Some(id) => Created(ApiResponse.success(Map("id" -> id)).toJson).as(ContentTypes.JSON)
        case None => InternalServerError(ApiResponse.error[Role](500, "保存实体失败，未能获取ID").toJson).as(ContentTypes.JSON)
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def deptUpdate(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, classOf[Department])
      if (entity.id.isEmpty) {
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "更新请求必须包含ID").toJson).as(ContentTypes.JSON))
      } else {
        service.updateDept(entity).map {
          case 0 => NotFound(ApiResponse.error[Role](404, "实体不存在或无法更新").toJson).as(ContentTypes.JSON)
          case n => Ok(ApiResponse.success(Map("updated" -> n)).toJson).as(ContentTypes.JSON)
        }
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def deptDelete(id: String): Action[AnyContent] = Action.async {
    service.deleteDept(id).map {
      case 0 => NotFound(ApiResponse.error[Role](404, "实体不存在或无法删除").toJson).as(ContentTypes.JSON)
      case n => Ok(ApiResponse.success(Map("deleted" -> n)).toJson).as(ContentTypes.JSON)
    }
  }


  def userList(page: Int, pageSize: Int): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
      try {
        val search = User.Search(
          deptId = (json \ "deptId").asOpt[String],
          phone = (json \ "phone").as[String],
          status = (json \ "status").asOpt[Long],
          username = (json \ "username").as[String]
        )
        service.userList(page, pageSize, search).map { case (entities, total) =>
          val resultMap = Map(
            "list" -> entities,
            "currentPage" -> page,
            "pageSize" -> pageSize,
            "total" -> total,
            "totalPages" -> math.ceil(total.toDouble / pageSize).toInt
          )
          Ok(ApiResponse.success(resultMap).toJson).as(ContentTypes.JSON)
        }
      } catch {
        case e: Exception =>
          Future.successful(BadRequest(
            ApiResponse.error[Role](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
      }
    }.getOrElse {
      Future.successful(BadRequest(
        ApiResponse.error[Role](400, "无效的请求数据", Seq("请求体必须是 JSON 格式")).toJson).as(ContentTypes.JSON))
    }
  }

  def userAdd(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, classOf[User])
      service.addUser(entity).map {
        case Some(id) => Created(ApiResponse.success(Map("id" -> id)).toJson).as(ContentTypes.JSON)
        case None => InternalServerError(ApiResponse.error[User](500, "保存实体失败，未能获取ID").toJson).as(ContentTypes.JSON)
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[User](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def userUpdate(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, classOf[User])
      if (entity.id.isEmpty) {
        Future.successful(BadRequest(
          ApiResponse.error[User](400, "更新请求必须包含ID").toJson).as(ContentTypes.JSON))
      } else {
        service.updateUser(entity).map {
          case 0 => NotFound(ApiResponse.error[User](404, "实体不存在或无法更新").toJson).as(ContentTypes.JSON)
          case n => Ok(ApiResponse.success(Map("updated" -> n)).toJson).as(ContentTypes.JSON)
        }
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[User](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def userDelete(id: String): Action[AnyContent] = Action.async {
    service.deleteUser(id).map {
      case 0 => NotFound(ApiResponse.error[User](404, "实体不存在或无法删除").toJson).as(ContentTypes.JSON)
      case n => Ok(ApiResponse.success(Map("deleted" -> n)).toJson).as(ContentTypes.JSON)
    }
  }

  def userDeletes(): Action[AnyContent] = Action.async { implicit request =>
    val ids = request.body.asJson.flatMap(json => (json \ "ids").asOpt[Seq[String]]).getOrElse(Seq.empty)
    service.deleteUsers(ids).map {
      case 0 => NotFound(ApiResponse.error[User](404, "实体不存在或无法删除").toJson).as(ContentTypes.JSON)
      case n => Ok(ApiResponse.success(Map("deleted" -> n)).toJson).as(ContentTypes.JSON)
    }
  }

  def resetPassword(): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
        try {

          val passwordData = User.PasswordReset(
            userId = (json \ "userId").as[String],
            newPassword = (json \ "newPwd").as[String]
          )
          service.resetPassword(passwordData).map {
            case true => Ok(ApiResponse.success(Map("success" -> true)).toJson).as(ContentTypes.JSON)
            case false => BadRequest(ApiResponse.error[User](400, "密码重置失败").toJson).as(ContentTypes.JSON)
          }
        } catch {
          case e: Exception =>
            Future.successful(BadRequest(
              ApiResponse.error[User](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
        }
      }
      .getOrElse {
        Future.successful(BadRequest(
          ApiResponse.error[User](400, "无效的请求数据", Seq("请求体必须是 JSON 格式")).toJson).as(ContentTypes.JSON))
      }
  }

  def userSetAvata() = Action.async(parse.multipartFormData) { request =>
    request.body
      .file("file")
      .map { filePart =>
        // Extract user ID from request
        request.body.asFormUrlEncoded.get("userId").flatMap(_.headOption) match {
          case Some(userId) =>
            try {

              // Generate a unique file name for the avatar
              val fileName = s"avatar_${userId}_${System.currentTimeMillis()}.jpg"

              // Process file upload - using a simpler approach
              fileTransferService.multipartFileUpload(
                filePart,
                fileName,
                "fs",
                request
              ).flatMap { result =>
                // Convert result body to string
                val bodyFuture = result.body.consumeData(mat).map(_.utf8String)

                bodyFuture.flatMap { body =>
                  // Log the response for debugging
                  log.info(s"File upload response: $body")

                  try {
                    // Use a more flexible approach to extract fileRef
                    import play.api.libs.json._
                    val json = Json.parse(body)
                    val fileRef = (json \\ "fileRef").headOption.map(_.as[String])

                    fileRef match {
                      case Some(ref) =>
                        service.updateUserAvatar(userId, ref).map { updated =>
                          if (updated > 0) {
                            Ok(ApiResponse.success(Map("url" -> (downloadUrlPrefix + ref))).toJson).as(ContentTypes.JSON)
                          } else {
                            InternalServerError(ApiResponse.error("Failed to update user avatar").toJson).as(ContentTypes.JSON)
                          }
                        }
                      case None =>
                        Future.successful(InternalServerError(ApiResponse.error("Failed to extract file reference").toJson).as(ContentTypes.JSON))
                    }
                  } catch {
                    case e: Exception =>
                      log.error(s"Error processing upload response: ${e.getMessage}", e)
                      Future.successful(InternalServerError(ApiResponse.error(s"Failed to process upload response: ${e.getMessage}").toJson).as(ContentTypes.JSON))
                  }
                }
              }.recover { case e: Exception =>
                log.error(s"Avatar upload failed: ${e.getMessage}", e)
                InternalServerError(ApiResponse.error(s"Avatar upload failed: ${e.getMessage}").toJson).as(ContentTypes.JSON)
              }
            } catch {
              case e: NumberFormatException =>
                Future.successful(BadRequest(ApiResponse.error("Invalid user ID format").toJson).as(ContentTypes.JSON))
            }
          case None =>
            Future.successful(BadRequest(ApiResponse.error("Missing user ID").toJson).as(ContentTypes.JSON))
        }
      }
      .getOrElse {
        Future.successful(BadRequest(ApiResponse.error("Missing avatar file").toJson).as(ContentTypes.JSON))
      }
  }
}