package modules.org.models

import core.models.BaseEntity

import java.time.OffsetDateTime

/**
 * 岗位实体
 *
 * @param id        岗位ID
 * @param code      岗位编码
 * @param name      岗位名称
 * @param status    状态（0正常 1停用）
 * @param sort      排序号
 * @param remark    备注
 * @param createdAt 创建时间
 * @param updatedAt 更新时间
 * @param deleted   是否删除
 */
case class Position(
                     var id: Option[String] = None,
                     code: String,
                     name: String,
                     status: Long = 0,
                     sort: Long = 0,
                     remark: Option[String] = None,
                     createdAt: OffsetDateTime = BaseEntity.now,
                     updatedAt: OffsetDateTime = BaseEntity.now,
                     deleted: Boolean = BaseEntity.DEFAULT_DELETED
                   ) extends BaseEntity