package modules.org.models

import core.BaseTable
import slick.jdbc.MySQLProfile.api._

import java.time.OffsetDateTime

/**
 * 岗位表映射
 * 将Position实体映射到数据库表结构
 */
class PositionTable(tag: Tag) extends Table[Position](tag, "org_positions") with BaseTable[Position] {
  // 基础字段
  def id = column[Option[String]]("id", <PERSON><PERSON>)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // Position特有字段
  def code = column[String]("code", O.Length(50))
  def name = column[String]("name_", O.Length(100))
  def status = column[Long]("status")
  def sort = column[Long]("sort")
  def remark = column[Option[String]]("remark")

  // 映射方法，将行数据转换为Position实体
  def * = (
    id, code, name, status, sort, remark, createdAt, updatedAt, deleted
  ) <> ((Position.apply _).tupled, Position.unapply)

  // 索引
  def codeIdx = index("idx_position_code", code, unique = true)
  def nameIdx = index("idx_position_name", name, unique = false)
}


/**
 * User表映射
 * 将User实体映射到数据库表结构
 */
class UserTable(tag: Tag) extends Table[User](tag, "org_users") with BaseTable[User] {
  // 基础字段
  def id = column[Option[String]]("id", O.PrimaryKey)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // User特有字段
  def deptId = column[Option[String]]("dept_id")
  def avatar = column[Option[String]]("avatar", O.Length(100))
  def username = column[String]("username", O.Length(100))
  def sex = column[Option[String]]("sex", O.Length(20))
  def password = column[String]("password", O.Length(255))
  def nickname = column[Option[String]]("nickname", O.Length(100))
  def email = column[Option[String]]("email", O.Length(100))
  def phone = column[Option[String]]("phone", O.Length(100))
  def description = column[Option[String]]("description")
  def status = column[Option[Long]]("status")

  // 映射方法，将行数据转换为User实体
  def * = (
    id, deptId, avatar, username, password, nickname, sex, email, phone, description, status, createdAt, updatedAt, deleted
  ) <> ((User.apply _).tupled, User.unapply)

  // 索引
  def usernameIdx = index("idx_username", username, unique = true)

}


/**
 * 用户-岗位关联表映射
 * 将UserPosition实体映射到数据库表结构
 */
class UserPositionTable(tag: Tag) extends Table[UserPosition](tag, "org_user_positions") with BaseTable[UserPosition] {
  // 基础字段
  def id = column[Option[String]]("id", O.PrimaryKey)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // UserPosition特有字段
  def userId = column[String]("user_id")
  def positionId = column[String]("position_id")
  def isPrimary = column[Boolean]("is_primary")

  // 映射方法，将行数据转换为UserPosition实体
  def * = (
    id, userId, positionId, isPrimary, createdAt, updatedAt, deleted
  ) <> ((UserPosition.apply _).tupled, UserPosition.unapply)

  // 索引
  def userPositionIdx = index("idx_user_position", (userId, positionId), unique = true)

  // 外键关联
  def userFk = foreignKey("fk_user_position_user", userId, TableQuery[UserTable])(_.id.get, onUpdate = ForeignKeyAction.Restrict, onDelete = ForeignKeyAction.Cascade)
  def positionFk = foreignKey("fk_user_position_position", positionId, TableQuery[PositionTable])(_.id.get, onUpdate = ForeignKeyAction.Restrict, onDelete = ForeignKeyAction.Cascade)
}


/**
 * 部门表映射
 * 将Department实体映射到数据库表结构
 */
class DepartmentTable(tag: Tag) extends Table[Department](tag, "org_departments") with BaseTable[Department] {
  // 基础字段
  def id = column[Option[String]]("id", O.PrimaryKey)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // Department特有字段
  def parentId = column[Option[String]]("parent_id")
  def name = column[String]("name_", O.Length(100))
  def principal = column[String]("principal", O.Length(100))
  def phone = column[String]("phone", O.Length(20))
  def email = column[String]("email", O.Length(100))
  def sort = column[Long]("sort")
  def status = column[Long]("status")
  def remark = column[Option[String]]("remark")

  // 映射方法，将行数据转换为Department实体
  def * = (
    id, parentId, name, principal, phone, email, sort, status, remark, createdAt, updatedAt, deleted
  ) <> ((Department.apply _).tupled, Department.unapply)

  // 索引
  def nameIdx = index("idx_dept_name", name, unique = false)
  def parentIdIdx = index("idx_parent_id", parentId, unique = false)
}