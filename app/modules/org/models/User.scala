package modules.org.models

import core.models.BaseEntity
import modules.org.models.Department.SimpleDept0
import play.api.libs.json._

import java.time.OffsetDateTime

/**
 * 用户实体
 *
 * @param id          用户ID
 * @param deptId      部门ID
 * @param avatar      头像
 * @param username    用户名
 * @param password    密码
 * @param nickname    昵称
 * @param email       邮箱
 * @param phone       手机号
 * @param description 描述
 * @param status      是否启用
 * @param createdAt   创建时间
 * @param updatedAt   更新时间
 * @param deleted     是否删除
 */
case class User(
                 var id: Option[String] = None,
                 deptId: Option[String] = None,
                 avatar: Option[String],
                 username: String,
                 password: String,
                 nickname: Option[String],
                 sex: Option[String],
                 email: Option[String],
                 phone: Option[String],
                 description: Option[String] = None,
                 status: Option[Long] = None,
                 createdAt: OffsetDateTime = BaseEntity.now,
                 updatedAt: OffsetDateTime = BaseEntity.now,
                 deleted: Boolean = BaseEntity.DEFAULT_DELETED
               ) extends BaseEntity

object User {

  case class Search(deptId: Option[String], phone: String, status: Option[Long], username: String)

  implicit val searchFormat: Format[Search] = Json.format[Search]

  case class SimpleUser(
                         id: Option[String],
                         avatar: String,
                         username: String,
                         nickname: Option[String],
                         phone: String,
                         email: Option[String],
                         sex: Option[Long] = None,
                         status: Long,
                         dept: SimpleDept0,
                         remark: String,
                         createTime: Option[Long] = None
                       )

  /**
   * 用户密码重置请求
   *
   * @param userId      用户ID
   * @param newPassword 新密码
   */
  case class PasswordReset(
                            userId: String,
                            newPassword: String
                          )

}