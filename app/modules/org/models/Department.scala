package modules.org.models

import core.models.BaseEntity

import java.time.OffsetDateTime

/**
 * 部门实体
 *
 * @param id        部门ID
 * @param parentId  父部门ID，顶级部门为null
 * @param name      部门名称
 * @param principal 部门负责人
 * @param phone     联系电话
 * @param email     联系邮箱
 * @param sort      排序号
 * @param status    状态（0正常 1停用）
 * @param remark    备注
 * @param createdAt 创建时间
 * @param updatedAt 更新时间
 * @param deleted   是否删除
 */
case class Department(
                       var id: Option[String] = None,
                       parentId: Option[String] = None,
                       name: String,
                       principal: String,
                       phone: String,
                       email: String,
                       sort: Long = 0,
                       status: Long = 0,
                       remark: Option[String] = None,
                       createdAt: OffsetDateTime = BaseEntity.now,
                       updatedAt: OffsetDateTime = BaseEntity.now,
                       deleted: Boolean = BaseEntity.DEFAULT_DELETED
                     ) extends BaseEntity


object Department {

  case class SimpleDept0(id: Option[String] = None, name: Option[String])

  case class SimpleDept(
                         id: Option[String] = None,
                         parentId: Option[String] = None,
                         name: String,
                         principal: String,
                         phone: String,
                         sort: Long = 0,
                         status: Long = 0,
                         remark: Option[String] = None,
                         createTime: OffsetDateTime = BaseEntity.now,
                         `type`: Option[String] = None
                       )

  case class TreeNode(
                       id: Option[String],
                       parentId: Option[String],
                       name: String,
                       principal: String,
                       phone: String,
                       email: String,
                       sort: Long,
                       status: Long,
                       remark: Option[String],
                       children: Seq[TreeNode] = Seq.empty
                     )
}
