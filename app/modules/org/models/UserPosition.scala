package modules.org.models

import core.models.BaseEntity

import java.time.OffsetDateTime

/**
 * 用户-岗位关联实体
 *
 * @param id         关联ID
 * @param userId     用户ID
 * @param positionId 岗位ID
 * @param isPrimary  是否主岗位 (1是 0否)
 * @param createdAt  创建时间
 * @param updatedAt  更新时间
 * @param deleted    是否删除
 */
case class UserPosition(
                         var id: Option[String] = None,
                         userId: String,
                         positionId: String,
                         isPrimary: Boolean = false,
                         createdAt: OffsetDateTime = BaseEntity.now,
                         updatedAt: OffsetDateTime = BaseEntity.now,
                         deleted: Boolean = BaseEntity.DEFAULT_DELETED
                       ) extends BaseEntity