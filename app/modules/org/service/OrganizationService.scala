package modules.org.service

import modules.org.models.Department.SimpleDept
import modules.org.models.User.SimpleUser
import modules.org.models.{Department, User}

import scala.concurrent.Future


trait OrganizationService {

  /**
   * 通过用户名查找用户
   *
   * @param username 用户名
   * @return Future Option of User
   */
  def findUserByUsername(username: String): Future[Option[User]]

  def findUserById(userId: String): Future[Option[User]]

  def userInfo(userId: String): Future[SimpleUser]

  def deptList(): Future[Seq[SimpleDept]]

  def addDept(entity: Department): Future[Option[String]]

  def updateDept(entity: Department): Future[Int]

  def deleteDept(id: String): Future[Int]

  def userList(page: Int, pageSize: Int, search: User.Search): Future[(Seq[SimpleUser], Int)]

  /**
   * 添加用户
   *
   * @param entity 用户实体
   * @return 如果添加成功则返回用户ID，否则返回None
   */
  def addUser(entity: User): Future[Option[String]]

  /**
   * 更新用户
   *
   * @param entity 用户实体
   * @return 更新的记录数
   */
  def updateUser(entity: User): Future[Int]

  /**
   * 删除用户
   *
   * @param id 用户ID
   * @return 删除的记录数
   */
  def deleteUser(id: String): Future[Int]

  def deleteUsers(ids: Seq[String]): Future[Int]

  /**
   * 重置用户密码
   *
   * @param passwordReset 密码重置信息
   * @return 是否成功
   */
  def resetPassword(passwordReset: User.PasswordReset): Future[Boolean]

  /**
   * Update a user's avatar
   *
   * @param userId  The user ID
   * @param fileRef The file reference for the avatar
   * @return true if update successful, false otherwise
   */
  def updateUserAvatar(userId: String, fileRef: String): Future[Int]

}
