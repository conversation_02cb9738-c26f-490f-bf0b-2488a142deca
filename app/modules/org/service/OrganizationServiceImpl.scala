package modules.org.service

import jakarta.inject.{Inject, Singleton}
import modules.file.Constant.downloadUrlPrefix
import modules.org.models.Department.{SimpleDept, SimpleDept0}
import modules.org.models.User.SimpleUser
import modules.org.models.{Department, User}
import modules.org.repository.OrganizationRepository
import services.AuthService
import uitls.RSAUtil

import java.time.OffsetDateTime
import scala.concurrent.{ExecutionContext, Future}


@Singleton
class OrganizationServiceImpl @Inject()(repository: OrganizationRepository)
                                       (implicit ec: ExecutionContext) extends OrganizationService {

  /**
   * 通过用户名查找用户
   *
   * @param username 用户名
   * @return Future Option of User
   */
  def findUserByUsername(username: String): Future[Option[User]] = repository.findUserByUsername(username)

  /**
   * 通过用户ID查找用户
   *
   * @param userId 用户ID
   * @return Future Option of User
   */
  def findUserById(userId: String): Future[Option[User]] = repository.findUserById(userId)

  /**
   * 获取用户信息
   *
   * @param userId 用户ID
   * @return Future SimpleUser
   */
  def userInfo(userId: String): Future[SimpleUser] = {
    findUserById(userId).flatMap {
      case Some(user) =>
        repository.findDeptNameById(user.deptId.getOrElse("")).map { deptNameOpt =>
          SimpleUser(
            id = user.id,
            avatar = user.avatar.getOrElse(""),
            username = user.username,
            nickname = user.nickname,
            email = user.email,
            phone = user.phone.getOrElse(""),
            remark = user.description.getOrElse(""),
            dept = SimpleDept0(user.deptId, deptNameOpt),
            status = user.status.getOrElse(0L),
          )
        }
      case None => throw new Exception(s"User with id $userId not found")
    }
  }

  def deptList(): Future[Seq[SimpleDept]] = {
    repository.deptList().map { list =>
      list.map(dept => SimpleDept(
        dept.id,
        dept.parentId,
        dept.name,
        dept.principal,
        dept.phone,
        dept.sort,
        dept.status,
        dept.remark,
        dept.createdAt
      ));
    }
  }

  def addDept(entity: Department): Future[Option[String]] = repository.addDept(entity)

  def updateDept(entity: Department): Future[Int] = repository.updateDept(entity)

  def deleteDept(id: String): Future[Int] = repository.deleteDept(id)


  def userList(page: Int, pageSize: Int, search: User.Search): Future[(Seq[SimpleUser], Int)] = {
    repository.userPage(page, pageSize, search).flatMap { case (entities, total) =>
      val deptFutures = entities.map { user =>
        repository.findDeptNameById(user.deptId.getOrElse("")).map(deptNameOpt => (user, deptNameOpt))
      }

      Future.sequence(deptFutures).map { userDeptPairs =>
        val simpleUsers = userDeptPairs.map {
          case (user, deptName) =>
            SimpleUser(
              id = user.id,
              avatar = user.avatar.getOrElse(""),
              username = user.username,
              nickname = user.nickname,
              sex = Some(java.lang.Long.parseLong(user.sex.getOrElse("0"))),
              phone = user.phone.getOrElse(""),
              email = user.email,
              status = user.status.getOrElse(0L),
              dept = SimpleDept0(user.deptId, deptName),
              remark = user.description.getOrElse(""),
              createTime = Some(user.createdAt.toEpochSecond * 1000)
            )
        }
        (simpleUsers, total)
      }
    }
  }

  /**
   * 添加用户
   * 处理用户密码加密等逻辑
   *
   * @param entity 用户实体
   * @return 如果添加成功则返回用户ID，否则返回None
   */
  def addUser(entity: User): Future[Option[String]] = {
    // 处理密码加密，这里假设entity.username作为盐值
    val salt = entity.username
    val defaultPwd = "Td123456"
    val encryptedPassword = AuthService.createUserPassword(defaultPwd, salt)

    // 创建带加密密码的用户
    val userWithEncryptedPassword = entity.copy(password = encryptedPassword)

    // 调用repository进行保存
    repository.addUser(userWithEncryptedPassword)
  }

  /**
   * 更新用户
   *
   * @param entity 用户实体
   * @return 更新的记录数
   */
  def updateUser(entity: User): Future[Int] = {
    // 先获取现有用户
    entity.id match {
      case Some(id) =>
        findUserById(id).flatMap {
          case Some(existingUser) =>
            // 调用repository进行更新
            val updatedUser = existingUser.copy(
              deptId = entity.deptId.orElse(existingUser.deptId),
              avatar = entity.avatar.orElse(existingUser.avatar),
              nickname = entity.nickname.orElse(existingUser.nickname),
              sex = entity.sex.orElse(existingUser.sex),
              email = entity.email.orElse(existingUser.email),
              phone = entity.phone.orElse(existingUser.phone),
              description = entity.description.orElse(existingUser.description),
              status = entity.status,
              updatedAt = OffsetDateTime.now
            )
            repository.updateUser(updatedUser)

          case None => Future.successful(0)
        }
      case None => Future.successful(0)
    }
  }

  /**
   * 删除用户
   *
   * @param id 用户ID
   * @return 删除的记录数
   */
  def deleteUser(id: String): Future[Int] = repository.deleteUser(id)

  def deleteUsers(ids: Seq[String]): Future[Int] = repository.deleteUsers(ids)

  /**
   * 重置用户密码
   *
   * @param passwordReset 密码重置信息
   * @return 是否成功
   */
  def resetPassword(passwordReset: User.PasswordReset): Future[Boolean] = {
    // 查找用户
    findUserById(passwordReset.userId).flatMap {
      case Some(user) =>
        // 解密原始密码
        val decryptedPassword = RSAUtil.decrypt(passwordReset.newPassword, RSAUtil.PRIVATE_KEY)
        // 加密新密码
        val salt = user.username
        val encryptedPassword = AuthService.createUserPassword(decryptedPassword, salt)

        // 更新密码
        val updatedUser = user.copy(password = encryptedPassword)
        repository.updateUser(updatedUser).map(_ > 0)

      case None => Future.successful(false)
    }
  }

  /**
   * Update a user's avatar
   *
   * @param userId  The user ID
   * @param fileRef The file reference for the avatar
   * @return true if update successful, false otherwise
   */
  def updateUserAvatar(userId: String, fileRef: String): Future[Int] = {
    repository.updateUserAvatar(userId, downloadUrlPrefix + fileRef)
  }

}