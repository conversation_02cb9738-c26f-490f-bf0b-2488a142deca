package modules.menu.models

import core.BaseTable
import slick.jdbc.MySQLProfile.api._
import slick.lifted.{ProvenShape, Tag}

import java.time.OffsetDateTime

/**
 * 菜单表定义
 *
 * @param tag 表标签
 */
class MenuTable(tag: Tag) extends Table[Menu](tag, "sys_menus") with BaseTable[Menu] {

  // 基础字段
  def id = column[Option[String]]("id", <PERSON><PERSON>ey)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // 菜单字段
  def path = column[String]("path_")
  def name = column[String]("name_")
  def menuType = column[Option[Long]]("menu_type")
  def redirect = column[Option[String]]("redirect")
  def title = column[String]("title")
  def icon = column[Option[String]]("icon")
  def extraIcon = column[Option[String]]("extra_icon")
  def showLink = column[Boolean]("show_link")
  def showParent = column[Boolean]("show_parent")
  def roles = column[Option[String]]("roles")
  def auths = column[Option[String]]("auths")
  def keepAlive = column[Boolean]("keep_alive")
  def frameSrc = column[Option[String]]("frame_src")
  def frameLoading = column[Boolean]("frame_loading")
  def transitionName = column[Option[String]]("transition_name")
  def enterTransition = column[Option[String]]("enter_transition")
  def leaveTransition = column[Option[String]]("leave_transition")
  def hiddenTag = column[Boolean]("hidden_tag")
  def fixedTag = column[Boolean]("fixed_tag")
  def rank = column[Option[Long]]("rank_")
  def dynamicLevel = column[Option[Long]]("dynamic_level")
  def activePath = column[Option[String]]("active_path")
  def component = column[Option[String]]("component")
  def parentId = column[Option[String]]("parent_id")

  /**
   * 表映射定义
   *
   * 问题描述：在 Slick 中，存在一个限制，即单个表映射（Table）最多只能有 22 个字段。这是由于 Scala 的元组（Tuple）最多只能有 22 个元素。如果你的表超过了 22 个字段，你需要采取一些策略来绕过这个限制。
   *
   * 解决方案：使用嵌套元组来绕过 22 字段的限制。将字段分组到两个元组中。
   */
  override def * : ProvenShape[Menu] = {
    // 将字段分成两组
    val part1 = (
      id, path, name, menuType, redirect, title, icon, extraIcon, showLink,
      showParent, roles, auths, keepAlive, frameSrc
    )

    val part2 = (
      frameLoading, transitionName, enterTransition, leaveTransition,
      hiddenTag, fixedTag, rank, dynamicLevel, activePath, component,
      parentId, createdAt, updatedAt, deleted
    )

    // 组合这两个元组并映射到Menu
    (part1, part2) <> ( {
      case (p1, p2) =>
        val (id, path, name, menuType, redirect, title, icon, extraIcon, showLink,
        showParent, roles, auths, keepAlive, frameSrc) = p1
        val (frameLoading, transitionName, enterTransition, leaveTransition,
        hiddenTag, fixedTag, rank, dynamicLevel, activePath, component,
        parentId, createdAt, updatedAt, deleted) = p2

        Menu(id, path, name, menuType, redirect, title, icon, extraIcon, showLink,
          showParent, roles, auths, keepAlive, frameSrc, frameLoading,
          transitionName, enterTransition, leaveTransition, hiddenTag,
          fixedTag, rank, dynamicLevel, activePath, component, parentId,
          createdAt, updatedAt, deleted)
    }, { menu: Menu =>
      Some((
        (menu.id, menu.path, menu.name, menu.menuType, menu.redirect, menu.title,
          menu.icon, menu.extraIcon, menu.showLink, menu.showParent,
          menu.roles, menu.auths, menu.keepAlive, menu.frameSrc),
        (menu.frameLoading, menu.transitionName, menu.enterTransition,
          menu.leaveTransition, menu.hiddenTag, menu.fixedTag, menu.rank, menu.dynamicLevel,
          menu.activePath, menu.component, menu.parentId, menu.createdAt,
          menu.updatedAt, menu.deleted)
      ))
    }
    )
  }

  /**
   * 索引定义
   */
  def nameIndex = index("idx_menu_name", name, unique = false)

  def pathIndex = index("idx_menu_path", path, unique = false)

  def parentIdIndex = index("idx_menu_parent_id", parentId, unique = false)
}