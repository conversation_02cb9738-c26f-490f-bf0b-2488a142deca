package modules.authority.controller

import core.auth.{SecuredAction, SecuredInfoAction}
import core.models.ApiResponse
import core.utils.JsonUtils
import jakarta.inject.Inject
import modules.authority.models.Role
import modules.authority.models.Role.SimpleRole
import modules.authority.models.RolePermission.RoleMenus
import modules.authority.service.AuthorityService
import modules.org.models.User
import play.api.http.ContentTypes
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

import scala.concurrent.{ExecutionContext, Future}


/**
 * 角色控制器
 */
class AuthorityController @Inject()(service: AuthorityService,
                                    securedAction: SecuredAction,
                                    securedInfoAction: SecuredInfoAction,
                                    val cc: ControllerComponents)
                                   (implicit ec: ExecutionContext) extends AbstractController(cc) {

  def roleAdd(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, service.getRoleClass)
      service.addRole(entity).map {
        case Some(id) => Created(ApiResponse.success(Map("id" -> id)).toJson).as(ContentTypes.JSON)
        case None => InternalServerError(ApiResponse.error[Role](500, "保存实体失败，未能获取ID").toJson).as(ContentTypes.JSON)
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def roleUpdate(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, service.getRoleClass)
      if (entity.id.isEmpty) {
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "更新请求必须包含ID").toJson).as(ContentTypes.JSON))
      } else {
        service.updateRole(entity).map {
          case 0 => NotFound(ApiResponse.error[Role](404, "实体不存在或无法更新").toJson).as(ContentTypes.JSON)
          case n => Ok(ApiResponse.success(Map("updated" -> n)).toJson).as(ContentTypes.JSON)
        }
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def roleDelete(id: String): Action[AnyContent] = Action.async {
    service.deleteRole(id).map {
      case 0 => NotFound(ApiResponse.error[Role](404, "实体不存在或无法删除").toJson).as(ContentTypes.JSON)
      case n => Ok(ApiResponse.success(Map("deleted" -> n)).toJson).as(ContentTypes.JSON)
    }
  }

  def roles(page: Int, pageSize: Int): Action[AnyContent] = Action.async {
    service.rolePage(page, pageSize).map { case (entities, total) =>
      val resultMap = Map(
        "list" -> entities.map { role =>
          SimpleRole(
            role.id.get,
            role.name,
            role.code,
            role.status.getOrElse(0L),
            role.description.getOrElse(""))
        },
        "currentPage" -> page,
        "pageSize" -> pageSize,
        "total" -> total,
        "totalPages" -> math.ceil(total.toDouble / pageSize).toInt
      )
      Ok(ApiResponse.success(resultMap).toJson).as(ContentTypes.JSON)
    }
  }

  def roleMenuIds(id: String): Action[AnyContent] = Action.async {
    service.roleMenuIds(id).map { datas =>
      Ok(ApiResponse.success(datas).toJson).as(ContentTypes.JSON)
    }
  }


  def roleMenuSave(): Action[AnyContent] = Action.async { implicit request =>
    try {
      val entity = JsonUtils.parseRequest(request, classOf[RoleMenus])
      service.saveRoleMenus(entity.roleId, entity.menuIds).map { result =>
        Ok(ApiResponse.success(Map("updated" -> result)).toJson).as(ContentTypes.JSON)
      }.recover {
        case e: Exception =>
          InternalServerError(ApiResponse.error[Role](500, "保存角色菜单关联失败", Seq(e.getMessage)).toJson).as(ContentTypes.JSON)
      }
    } catch {
      case e: Exception =>
        Future.successful(BadRequest(
          ApiResponse.error[Role](400, "无效的请求数据", Seq(e.getMessage)).toJson).as(ContentTypes.JSON))
    }
  }

  def roles2(): Action[AnyContent] = Action.async {
    service.roleList().map { datas =>
      val tuples = datas.map(role => Map("id" -> role.id.get, "name" -> role.name))
      Ok(ApiResponse.success(tuples).toJson).as(ContentTypes.JSON)
    }
  }

  def roles2user(): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
      val userId = (json \ "userId").as[String]
      val roleIds = (json \ "roleIds").as[Seq[String]]

      service.roles2user(userId, roleIds).map { data =>
        Ok(ApiResponse.success(data).toJson).as(ContentTypes.JSON)
      }.recover {
        case e: Exception =>
          InternalServerError(ApiResponse.error[User](500, "关联用户角色失败", Seq(e.getMessage)).toJson).as(ContentTypes.JSON)
      }
    }.getOrElse {
      Future.successful(BadRequest(
        ApiResponse.error[User](400, "无效的请求数据").toJson).as(ContentTypes.JSON))
    }
  }

  def userRoles(userid: String): Action[AnyContent] = Action.async {
    service.getUserRoles(userid).map { roles => roles.map(_.id) }
      .map(ids => Ok(ApiResponse.success(ids).toJson).as(ContentTypes.JSON))
  }
}