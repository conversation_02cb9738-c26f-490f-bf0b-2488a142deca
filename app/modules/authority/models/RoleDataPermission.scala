package modules.authority.models

import core.utils.Snowflake

import java.time.OffsetDateTime

/**
 * 角色数据权限关联实体类
 *
 * @param id               关联ID
 * @param roleId           角色ID
 * @param dataPermissionId 数据权限ID
 * @param createdAt        创建时间
 * @param updatedAt        更新时间
 */
case class RoleDataPermission(
                               var id: Option[String] = Some(Snowflake.nextIdStr()),
                               roleId: String,
                               dataPermissionId: String,
                               createdAt: OffsetDateTime = OffsetDateTime.now(),
                               updatedAt: OffsetDateTime = OffsetDateTime.now()
                             ) 