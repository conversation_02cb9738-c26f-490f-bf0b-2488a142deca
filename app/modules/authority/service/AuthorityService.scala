package modules.authority.service

import modules.authority.models.{Permission, Resource, Role}

import scala.concurrent.Future

/**
 * 权限服务接口
 */
trait AuthorityService {

  def getRoleClass: Class[Role] = classOf[Role]

  def getResourceClass: Class[Resource] = classOf[Resource]

  def getPermissionClass: Class[Permission] = classOf[Permission]

  /**
   * 获取用户角色
   *
   * @param userId 用户ID
   * @return 角色列表
   */
  def getUserRoles(userId: String): Future[Seq[Role]]

  /**
   * 获取用户权限
   *
   * @param userId 用户ID
   * @return 权限列表
   */
  def getUserPermissions(userId: String): Future[Seq[Permission]]

  /**
   * 获取用户角色代码
   *
   * @param userId 用户ID
   * @return 角色代码列表
   */
  def getUserRoleCodes(userId: String): Future[Seq[String]]

  /**
   * 获取用户权限代码
   *
   * @param userId 用户ID
   * @return 权限代码列表
   */
  def getUserPermissionCodes(userId: String): Future[Seq[String]]

  def addRole(entity: Role): Future[Option[String]]

  def roleList(): Future[Seq[Role]]

  def rolePage(page: Int, pageSize: Int): Future[(Seq[Role], Int)]

  def roleMenuIds(id: String): Future[Seq[String]]

  def deleteRole(id: String): Future[Int]

  def updateRole(entity: Role): Future[Int]

  /**
   * 保存角色菜单关联
   *
   * @param roleId  角色ID
   * @param menuIds 菜单ID列表
   * @return 更新数量
   */
  def saveRoleMenus(roleId: String, menuIds: Seq[String]): Future[Int]

  def roles2user(userId: String, roleIds: Seq[String]): Future[Int]
}
