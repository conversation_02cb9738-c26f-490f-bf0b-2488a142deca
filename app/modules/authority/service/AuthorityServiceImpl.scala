package modules.authority.service

import jakarta.inject.{Inject, Singleton}
import modules.authority.models.{Permission, Role}
import modules.authority.repository.AuthorityRepository

import scala.concurrent.{ExecutionContext, Future}


/**
 * 权限服务实现
 *
 * @param repository 数据库
 * @param ec         执行上下文
 */
@Singleton
class AuthorityServiceImpl @Inject()(
                                      val repository: AuthorityRepository
                                    )(implicit ec: ExecutionContext) extends AuthorityService {


  /**
   * 获取用户角色
   *
   * @param userId 用户ID
   * @return 角色列表
   */
  def getUserRoles(userId: String): Future[Seq[Role]] = repository.getUserRoles(userId)

  /**
   * 获取用户权限
   *
   * @param userId 用户ID
   * @return 权限列表
   */
  def getUserPermissions(userId: String): Future[Seq[Permission]] = repository.getUserPermissions(userId)

  /**
   * 获取用户角色代码
   *
   * @param userId 用户ID
   * @return 角色代码列表
   */
  override def getUserRoleCodes(userId: String): Future[Seq[String]] = {
    getUserRoles(userId).map(_.map(_.code))
  }

  /**
   * 获取用户权限代码
   *
   * @param userId 用户ID
   * @return 权限代码列表
   */
  def getUserPermissionCodes(userId: String): Future[Seq[String]] = {
    getUserPermissions(userId).map(_.map(_.code))
  }

  def addRole(entity: Role): Future[Option[String]] = repository.addRole(entity)

  def roleList(): Future[Seq[Role]] = repository.roleList()

  def rolePage(page: Int, pageSize: Int): Future[(Seq[Role], Int)] = repository.rolePage(page, pageSize)

  def roleMenuIds(id: String): Future[Seq[String]] = repository.roleMenuIds(id)

  def deleteRole(id: String): Future[Int] = repository.deleteRole(id)

  def updateRole(entity: Role): Future[Int] = repository.updateRole(entity)

  /**
   * 保存角色菜单关联
   *
   * @param roleId  角色ID
   * @param menuIds 菜单ID列表
   * @return 更新数量
   */
  def saveRoleMenus(roleId: String, menuIds: Seq[String]): Future[Int] = repository.saveRoleMenus(roleId, menuIds)

  def roles2user(userId: String, roleIds: Seq[String]): Future[Int] = repository.roles2user(userId, roleIds)
}