package modules.authority.repository

import jakarta.inject.{Inject, Singleton}
import modules.authority.models.{Permission, Role, RolePermission, UserRole}
import modules.authority.models.{PermissionTable, RolePermissionTable, RoleTable, UserRoleTable}
import play.api.db.slick.{DatabaseConfigProvider, HasDatabaseConfigProvider}
import slick.jdbc.JdbcProfile
import slick.jdbc.MySQLProfile.api._

import java.time.OffsetDateTime
import scala.concurrent.{ExecutionContext, Future}

/**
 * 权限仓库实现
 */
@Singleton
class AuthorityRepository @Inject()(
                                     val dbConfigProvider: DatabaseConfigProvider
                                   )(implicit ec: ExecutionContext)
  extends HasDatabaseConfigProvider[JdbcProfile] {

  // 表查询对象
  private val userRolesTableQuery = TableQuery[UserRoleTable]
  private val rolesTableQuery = TableQuery[RoleTable]
  private val rolePermissionsTableQuery = TableQuery[RolePermissionTable]
  private val permissionsTableQuery = TableQuery[PermissionTable]

  /**
   * 获取用户角色
   *
   * @param userId 用户ID
   * @return 角色列表
   */
  def getUserRoles(userId: String): Future[Seq[Role]] = {
    val query = for {
      ur <- userRolesTableQuery if ur.userId === userId
      r <- rolesTableQuery if r.id === ur.roleId && r.deleted === false
    } yield r

    db.run(query.result)
  }

  /**
   * 获取用户权限
   *
   * @param userId 用户ID
   * @return 权限列表
   */
  def getUserPermissions(userId: String): Future[Seq[Permission]] = {
    val query = for {
      ur <- userRolesTableQuery if ur.userId === userId
      rp <- rolePermissionsTableQuery if rp.roleId === ur.roleId
      p <- permissionsTableQuery if p.id === rp.permissionId && p.deleted === false
    } yield p

    db.run(query.result)
  }

  def roleList(): Future[Seq[Role]] = {
    db.run(rolesTableQuery.filter(_.deleted === false).result)
  }

  def roleMenuIds(id: String): Future[Seq[String]] = {
    db.run(rolePermissionsTableQuery.filter(_.roleId === id).map(_.permissionId).result)
  }

  def rolePage(page: Int, pageSize: Int): Future[(Seq[Role], Int)] = {
    val offset = (page - 1) * pageSize

    // Query for total count (for pagination)
    val totalQuery = rolesTableQuery.filter(_.deleted === false).length.result

    // Query for data with pagination
    val dataQuery = rolesTableQuery
      .filter(_.deleted === false)
      .drop(offset)
      .take(pageSize)
      .result

    // Run both queries in parallel and combine results
    db.run(dataQuery zip totalQuery)
  }

  def addRole(entity: Role): Future[Option[String]] = {
    db.run(
      (rolesTableQuery returning rolesTableQuery.map(_.id)) += entity
    )
  }

  def deleteRole(id: String): Future[Int] = {
    db.run(
      rolesTableQuery.filter(_.id === id).map(_.deleted).update(true)
    )
  }

  def updateRole(entity: Role): Future[Int] = {
    entity.id match {
      case Some(id) =>
        db.run(
          rolesTableQuery
            .filter(_.id === id)
            .filter(_.deleted === false)
            .update(entity)
        )
      case None => Future.successful(0)
    }
  }

  /**
   * 保存角色菜单关联
   * 先删除该角色所有现有的菜单权限关联，然后添加新的关联
   *
   * @param roleId  角色ID
   * @param menuIds 菜单ID列表
   * @return 更新数量
   */
  def saveRoleMenus(roleId: String, menuIds: Seq[String]): Future[Int] = {

    // 创建新的角色-权限关联实体
    val newRolePermissions = menuIds.map { menuId =>
      RolePermission(roleId = roleId, permissionId = menuId)
    }

    // 事务: 先删除旧关联，再插入新关联
    val action = for {
      // 删除该角色的所有现有菜单关联
      _ <- rolePermissionsTableQuery.filter(_.roleId === roleId).delete
      // 批量插入新的关联
      count <- rolePermissionsTableQuery ++= newRolePermissions
    } yield count.getOrElse(0)

    // 执行事务
    db.run(action.transactionally)
  }

  def roles2user(userId: String, roleIds: Seq[String]): Future[Int] = {
    val now = OffsetDateTime.now()

    // 创建新的用户-角色关联实体
    val newUserRoles = roleIds.map { roleId =>
      UserRole(None, userId, roleId, now, now)
    }

    // 事务: 先删除旧关联，再插入新关联
    val action = for {
      // 删除该用户所有的角色关联
      _ <- userRolesTableQuery.filter(_.userId === userId).delete
      // 批量插入新的关联
      count <- userRolesTableQuery ++= newUserRoles
    } yield count.getOrElse(0)

    // 执行事务
    db.run(action.transactionally)
  }

}