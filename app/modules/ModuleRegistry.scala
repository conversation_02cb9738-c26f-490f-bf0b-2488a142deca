package modules

import com.google.inject.AbstractModule
import controllers.AuthController
import core.auth.{SecuredAction, SecuredInfoAction, UserContext}
import modules.authority.controller.AuthorityController
import modules.authority.repository.AuthorityRepository
import modules.authority.service.{AuthorityService, AuthorityServiceImpl}
import modules.crm.controller.{CustomerController, ProgressController}
import modules.crm.repository.{CustomerRepository, ProgressRepository}
import modules.crm.service.{CustomerService, CustomerServiceImpl, ProgressService, ProgressServiceImpl}
import modules.dict.controller.DataDictController
import modules.dict.repository.DataDictRepository
import modules.dict.service.{DataDictService, DataDictServiceImpl}
import modules.menu.controller.MenuController
import modules.menu.repository.{MenuRepository, MenuRepositoryImpl}
import modules.menu.service.{MenuService, MenuServiceImpl}
import modules.org.controller.OrganizationController
import modules.org.repository.OrganizationRepository
import modules.org.service.{OrganizationService, OrganizationServiceImpl}
import services.{AuthService, AuthServiceImpl}

/**
 * 模块注册类
 * 负责注册系统中的所有模块和服务
 * 使用Guice依赖注入框架
 */
class ModuleRegistry extends AbstractModule {

  /**
   * 配置绑定
   * 将接口绑定到实现类
   */
  override def configure(): Unit = {

    // 认证模块
    bind(classOf[AuthController])
    bind(classOf[AuthService]).to(classOf[AuthServiceImpl])

    // 认证上下文和Action
    bind(classOf[UserContext])
    bind(classOf[SecuredAction])
    bind(classOf[SecuredInfoAction])

    // 菜单模块
    bind(classOf[MenuController])
    bind(classOf[MenuService]).to(classOf[MenuServiceImpl])
    bind(classOf[MenuRepository]).to(classOf[MenuRepositoryImpl])

    // 角色权限模块
    bind(classOf[AuthorityController])
    bind(classOf[AuthorityRepository])
    bind(classOf[AuthorityService]).to(classOf[AuthorityServiceImpl])

    // 组织架构模块
    bind(classOf[OrganizationController])
    bind(classOf[OrganizationRepository])
    bind(classOf[OrganizationService]).to(classOf[OrganizationServiceImpl])

    // 数据字典模块
    bind(classOf[DataDictController])
    bind(classOf[DataDictRepository])
    bind(classOf[DataDictService]).to(classOf[DataDictServiceImpl])


    // 其他模块绑定将在模块实现后添加

    // CRM 王嘉
    bind(classOf[CustomerController])
    bind(classOf[ProgressController])
    
    bind(classOf[CustomerRepository])
    bind(classOf[CustomerService]).to(classOf[CustomerServiceImpl])

    bind(classOf[ProgressRepository])
    bind(classOf[ProgressService]).to(classOf[ProgressServiceImpl])

  }
} 