package modules.code

import com.google.inject.AbstractModule
import modules.code.controller.CodeController
import modules.code.repository.CodeRepository
import modules.code.service.{CodeService, CodeServiceImpl}

class Module extends AbstractModule {
  override def configure(): Unit = {
    bind(classOf[CodeController])
    bind(classOf[CodeRepository])
    bind(classOf[CodeService]).to(classOf[CodeServiceImpl])
  }
}
