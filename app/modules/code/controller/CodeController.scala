package modules.code.controller

import core.auth.{SecuredAction, SecuredInfoAction}
import jakarta.inject.Inject
import modules.code.service.CodeService
import org.apache.pekko.stream.Materializer
import org.slf4j.{Logger, LoggerFactory}
import play.api.mvc.{AbstractController, ControllerComponents}

class CodeController  @Inject()(
                                 mat: Materializer,
                                 service: CodeService,
                                 securedAction: SecuredAction,
                                 securedInfoAction: SecuredInfoAction,
                                 cc: ControllerComponents,
                               ) extends AbstractController(cc) {

  private val log: Logger = LoggerFactory.getLogger(classOf[CodeController])


}
