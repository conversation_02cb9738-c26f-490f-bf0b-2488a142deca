package modules.crm.repository

import core.AbstractBaseRepository
import jakarta.inject.Inject
import modules.crm.models.{Customer, CustomerTable}
import play.api.db.slick.DatabaseConfigProvider
import slick.jdbc.MySQLProfile.api._

import java.time.OffsetDateTime
import scala.concurrent.{ExecutionContext, Future}


/**
 * 客户仓库实现类
 *
 * @param dbConfigProvider 数据库配置提供者
 * @param ec               执行上下文
 */
class CustomerRepository @Inject()(
                                    override val dbConfigProvider: DatabaseConfigProvider
                                  )(implicit override val ec: ExecutionContext)
  extends AbstractBaseRepository[Customer, CustomerTable](dbConfigProvider) {

  override val tableQuery: TableQuery[CustomerTable] = TableQuery[CustomerTable]


  /**
   * 查找未分配销售人员的客户
   *
   * @return 未分配销售人员的客户列表
   */
  def findUnassigned(page: Int, pageSize: Int): Future[(Seq[Customer], Int)] = {

    val offset = (page - 1) * pageSize

    val tableQuery1 = tableQuery.filter(_.deleted === false).filter(_.salesmanId.isEmpty)
    val totalQuery = tableQuery1.length.result

    val dataQuery = tableQuery1
      .drop(offset)
      .take(pageSize)
      .result

    db.run(dataQuery zip totalQuery)
  }

  /**
   * 查找指定销售人员负责的客户
   *
   * @param salesmanId 销售人员ID
   * @return 该销售人员负责的客户列表
   */
  def findBySalesman(salesmanId: String, page: Int, pageSize: Int): Future[(Seq[Customer], Int)] = {
    val offset = (page - 1) * pageSize

    val totalQuery = tableQuery.filter(_.deleted === false).length.result

    val dataQuery = tableQuery
      .filter(_.deleted === false)
      .filter(_.salesmanId === salesmanId)
      .drop(offset)
      .take(pageSize)
      .result

    db.run(dataQuery zip totalQuery)
  }

  /**
   * 更新客户的销售人员分配信息
   *
   * @param customerId 客户ID
   * @param salesmanId 销售人员ID
   * @return 更新成功返回1，否则返回0
   */
  def assignSalesman(customerId: String, salesmanId: String): Future[Int] = {
    val now = OffsetDateTime.now()
    db.run(
      tableQuery
        .filter(_.id === customerId)
        .filter(_.deleted === false)
        .map(c => (c.salesmanId, c.assignedAt, c.updatedAt))
        .update((Some(salesmanId), Some(now), now))
    )
  }

  /**
   * 根据搜索条件动态添加过滤器
   * 覆盖父类方法，实现客户特定的搜索逻辑
   *
   * @param baseQuery 基础查询
   * @param search    搜索条件Map
   * @return 应用了搜索条件的查询
   */
  override protected def applySearchFilters(baseQuery: Query[CustomerTable, Customer, Seq], search: Map[String, Any]): Query[CustomerTable, Customer, Seq] = {
    var filteredQuery = baseQuery

    // 根据搜索条件动态添加过滤器
    search.foreach { case (key, value) =>
      key match {
        case "name" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.name like s"%${value.toString}%")
        case "linkMan" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.linkMan like s"%${value.toString}%")
        case "phone" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.phone like s"%${value.toString}%")
        case "mobile" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.mobile like s"%${value.toString}%")
        case "email" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.email like s"%${value.toString}%")
        case "source" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.source === value.toString)
        case "type_" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.type_ === value.toString)
        case "salesmanId" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.salesmanId === value.toString)
        case "country" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.country like s"%${value.toString}%")
        case "position" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.position like s"%${value.toString}%")
        case "assigned" if value != null =>
          value.toString.toLowerCase match {
            case "true" | "1" => filteredQuery = filteredQuery.filter(_.salesmanId.isDefined)
            case "false" | "0" => filteredQuery = filteredQuery.filter(_.salesmanId.isEmpty)
            case _ => // 忽略无效值
          }
        case _ => // 忽略未知的搜索字段
      }
    }

    // 添加默认排序
    filteredQuery.sortBy(_.createdAt.desc)
  }
}
