package modules.crm.repository

import core.AbstractBaseRepository
import jakarta.inject.Inject
import modules.crm.models.vo.ProgressItemVo
import modules.crm.models.{CustomerTable, Progress, ProgressTable}
import modules.org.models.UserTable
import play.api.db.slick.DatabaseConfigProvider
import slick.jdbc.MySQLProfile.api._

import scala.concurrent.{ExecutionContext, Future}


/**
 * 客户进度仓库实现类
 *
 * @param dbConfigProvider 数据库配置提供者
 * @param ec               执行上下文
 */
class ProgressRepository @Inject()(
                                    override protected val dbConfigProvider: DatabaseConfigProvider
                                  )(implicit override val ec: ExecutionContext)
  extends AbstractBaseRepository[Progress, ProgressTable](dbConfigProvider) {

  override val tableQuery: TableQuery[ProgressTable] = TableQuery[ProgressTable]
  private val customerTableQuery: TableQuery[CustomerTable] = TableQuery[CustomerTable]
  private val userTableQuery: TableQuery[UserTable] = TableQuery[UserTable]


  /**
   * 根据搜索条件动态添加过滤器
   * 子类可以覆盖此方法来实现具体的搜索逻辑
   *
   * @param baseQuery 基础查询
   * @param search    搜索条件Map
   * @return 应用了搜索条件的查询
   */
  override protected def applySearchFilters(baseQuery: Query[ProgressTable, Progress, Seq], search: Map[String, Any]): Query[ProgressTable, Progress, Seq] = {
    var filteredQuery = baseQuery

    // 根据搜索条件动态添加过滤器
    search.foreach { case (key, value) =>
      key match {
        case "customerId" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.customerId === value.toString)
        case "phase" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.phase like s"%${value.toString}%")
        case "status" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.status === value.toString)
        case "plan" if value != null && value.toString.nonEmpty =>
          filteredQuery = filteredQuery.filter(_.plan like s"%${value.toString}%")
        case _ => // 忽略未知的搜索字段
      }
    }

    // 添加默认排序
    filteredQuery.sortBy(_.createdAt.desc)
  }

  /**
   * 获取带有客户信息的进度数据（通过JOIN查询）
   *
   * @param salesmanId 销售人员ID
   * @param page       页码
   * @param pageSize   每页大小
   * @param searchMap  搜索条件
   * @return 带有客户信息的进度数据
   */
  def getProgressWithCustomerInfo(salesmanId: String,
                                  page: Int,
                                  pageSize: Int,
                                  searchMap: Map[String, String]): Future[(Seq[ProgressItemVo], Int)] = {

    // 构建三表JOIN查询：Progress -> Customer -> User(Salesman)
    val joinQuery = for {
      progress <- tableQuery.filter(_.deleted === false)
      customer <- customerTableQuery.filter(_.deleted === false) if progress.customerId === customer.id.getOrElse("")
      salesman <- userTableQuery.filter(_.deleted === false) if customer.salesmanId === salesman.id
      if customer.salesmanId === salesmanId
    } yield (progress, customer, salesman)

    // 应用搜索过滤器
    var filteredQuery = joinQuery

    // 根据搜索条件过滤
    searchMap.foreach { case (key, value) =>
      key match {
        case "customerId" if value != null && value.nonEmpty =>
          filteredQuery = filteredQuery.filter(_._1.customerId === value)
        case "customerName" if value != null && value.nonEmpty =>
          filteredQuery = filteredQuery.filter(_._2.name like s"%$value%")
        case "phase" if value != null && value.nonEmpty =>
          filteredQuery = filteredQuery.filter(_._1.phase like s"%$value%")
        case "status" if value != null && value.nonEmpty =>
          filteredQuery = filteredQuery.filter(_._1.status === value)
        case "salesmanName" if value != null && value.nonEmpty =>
          filteredQuery = filteredQuery.filter(_._3.nickname like s"%$value%")
        case _ => // 忽略未知的搜索字段
      }
    }

    // 查询总数
    val totalQuery = filteredQuery.length.result

    // 分页查询
    val offset = (page - 1) * pageSize
    val dataQuery = filteredQuery
      .drop(offset)
      .take(pageSize)
      .sortBy(_._1.createdAt.desc)
      .result

    // 执行查询并转换为ProgressItemVo
    db.run(dataQuery zip totalQuery).map { case (results, total) =>
      val progressItems = results.map { case (progress, customer, salesman) =>
        ProgressItemVo(
          id = progress.id.getOrElse(""),
          customerId = progress.customerId,
          customerName = customer.name,
          phase = progress.phase,
          plan = progress.plan,
          remark = progress.remark.getOrElse(""),
          salesmanId = salesmanId,
          salesmanName = salesman.nickname.getOrElse(salesman.username), // 使用真实的销售员名称
          status = progress.status,
          updatedAt = progress.updatedAt.toString
        )
      }
      (progressItems, total)
    }
  }
}
