package modules.crm.service

import core.BaseService
import modules.crm.models.CustomerTable
import modules.crm.models.Customer
import modules.crm.repository.CustomerRepository

import java.io.InputStream
import scala.concurrent.Future

/**
 * 客户服务接口，定义客户相关的业务逻辑
 */
trait CustomerService extends BaseService[Customer, CustomerTable, CustomerRepository] {
  /**
   * 获取未分配销售人员的客户列表
   *
   * @return 未分配销售人员的客户列表
   */
  def getUnassignedCustomers(page: Int, pageSize: Int): Future[(Seq[Customer], Int)]

  /**
   * 获取指定销售人员负责的客户
   *
   * @param salesmanId 销售人员ID
   * @return 该销售人员负责的客户列表
   */
  def getCustomersBySalesman(salesmanId: String,page: Int, pageSize: Int): Future[(Seq[Customer], Int)]

  /**
   * 将客户分配给销售人员
   *
   * @param customerId 客户ID
   * @param salesmanId 销售人员ID
   * @return 分配结果，1表示成功，0表示失败
   */
  def assignCustomerToSalesman(customerId: String, salesmanId: String): Future[Int]

  def processExcelFile(inputStream: InputStream): Future[(Int, Int, Int)]

  def setCustomerStatus(customerId: String, status: String): Future[Boolean]

  /**
   * 带搜索条件的分页查询客户
   *
   * @param page     页码
   * @param pageSize 每页大小
   * @param search   搜索条件Map
   * @return 分页结果
   */
  def searchCustomers(page: Int, pageSize: Int, search: Map[String, Any]): Future[(Seq[Customer], Int)]

}
