package modules.crm.service

import core.AbstractBaseService
import jakarta.inject.Inject
import modules.crm.models.ProgressTable
import modules.crm.models.Progress
import modules.crm.models.vo.ProgressItemVo
import modules.crm.repository.ProgressRepository

import scala.concurrent.{ExecutionContext, Future}


/**
 * 客户进度服务实现类
 *
 * @param repository 客户进度仓库
 * @param ec         执行上下文
 */
class ProgressServiceImpl @Inject()(
                                     override val repository: ProgressRepository
                                   )(implicit override val ec: ExecutionContext)
  extends AbstractBaseService[Progress, ProgressTable, ProgressRepository](repository) with ProgressService {

  /**
   * 获取实体类的Class对象
   *
   * @return Progress类的Class对象
   */
  def getEntityClass: Class[Progress] = classOf[Progress]

  /**
   * 设置客户状态
   *
   * @param salesmanId 销售人员ID
   * @param customerId 客户ID
   * @param progressId 进度ID
   * @param status     状态
   * @return 设置结果
   */
  def setProgressStatus(salesmanId: Option[String],
                        customerId: String,
                        progressId: Option[String],
                        status: String): Future[Boolean] = {
    Future.successful(true)
  }

  def progress(salesmanId: String,
               page: Int,
               pageSize: Int,
               searchMap: Map[String, String]): Future[(Seq[ProgressItemVo], Int)] = {

    repository.getProgressWithCustomerInfo(salesmanId, page, pageSize, searchMap)
  }

}
