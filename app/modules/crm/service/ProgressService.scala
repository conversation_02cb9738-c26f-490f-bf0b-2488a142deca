package modules.crm.service

import core.BaseService
import modules.crm.models.ProgressTable
import modules.crm.models.Progress
import modules.crm.models.vo.ProgressItemVo
import modules.crm.repository.ProgressRepository

import scala.concurrent.Future

/**
 * 客户进度服务接口，定义客户进度相关的业务逻辑
 */
trait ProgressService extends BaseService[Progress, ProgressTable, ProgressRepository] {

  /**
   * 设置客户状态
   *
   * @param salesmanId 销售人员ID
   * @param customerId 客户ID
   * @param progressId 进度ID
   * @param status     状态
   * @return 设置结果
   */
  def setProgressStatus(salesmanId: Option[String], customerId: String, progressId: Option[String], status: String): Future[Boolean]

  /**
   * 获取客户进度
   *
   * @param salesmanId 销售人员ID
   * @param page       页码
   * @param pageSize   页大小
   * @param searchMap  搜索条件Map
   * @return 客户进度列表
   */
  def progress(salesmanId: String, page: Int, pageSize: Int, searchMap: Map[String, String]): Future[(Seq[ProgressItemVo], Int)]

}
