package modules.crm.models.dto

// 客户公司 联系人	职位	国家	地址	电话	手机	网站	邮箱	客户来源	客户类型 描述
class CustomerExcel {
  var company: String = _
  var linkMan: String = _
  var position: String = _
  var country: String = _
  var address: String = _
  var phone: String = _
  var mobile: String = _
  var website: String = _
  var email: String = _
  var source: String = _
  var `type`: String = _
  var description: String = _

  // Get<PERSON> and Setters
  def getCompany: String = company

  def setCompany(company: String): Unit = {
    this.company = company
  }

  def getLinkMan: String = linkMan

  def setLinkMan(linkMan: String): Unit = {
    this.linkMan = linkMan
  }

  def getPosition: String = position

  def setPosition(position: String): Unit = {
    this.position = position
  }

  def getCountry: String = country

  def setCountry(country: String): Unit = {
    this.country = country
  }

  def getAddress: String = address

  def setAddress(address: String): Unit = {
    this.address = address
  }

  def getPhone: String = phone

  def setPhone(phone: String): Unit = {
    this.phone = phone
  }

  def getMobile: String = mobile

  def setMobile(mobile: String): Unit = {
    this.mobile = mobile
  }

  def getWebsite: String = website

  def setWebsite(website: String): Unit = {
    this.website = website
  }

  def getEmail: String = email

  def setEmail(email: String): Unit = {
    this.email = email
  }

  def getSource: String = source

  def setSource(source: String): Unit = {
    this.source = source
  }

  def getType: String = `type`

  def setType(`type`: String): Unit = {
    this.`type` = `type`
  }

  def getDescription: String = description

  def setDescription(description: String): Unit = {
    this.description = description
  }
}

