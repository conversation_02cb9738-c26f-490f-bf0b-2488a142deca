package modules.crm.models

import core.models.BaseEntity

import java.time.OffsetDateTime

/**
 * 表示具有各种属性的客户。
 *
 * @param id          客户的可选唯一标识符。
 * @param name        客户的名称。
 * @param position    客户的可选职位或头衔。
 * @param country     客户的可选国家。
 * @param address     客户的可选地址。
 * @param phone       客户的可选电话号码。
 * @param mobile      客户的可选手机号码。
 * @param website     客户的可选网站。
 * @param email       客户的可选电子邮件地址。
 * @param source      客户信息的来源。
 * @param type_       客户的类型。
 * @param description 客户的可选描述。
 * @param salesmanId  分配给客户的销售人员的可选ID。
 * @param assignedAt  客户被分配给销售人员的可选时间戳。
 * @param createdAt   创建客户记录的时间戳。
 * @param updatedAt   上次更新客户记录的时间戳。
 * @param deleted     指示客户记录是否已删除的标志。
 */
case class Customer(
                     var id: Option[String] = None,
                     name: String,
                     linkMan: String,
                     position: Option[String] = None,
                     country: Option[String] = None,
                     address: Option[String] = None,
                     phone: Option[String] = None,
                     mobile: Option[String] = None,
                     website: Option[String] = None,
                     email: Option[String] = None,
                     source: String,
                     type_ : String,
                     description: Option[String] = None,
                     salesmanId: Option[String] = None,
                     assignedAt: Option[OffsetDateTime] = None, // 分配时间（有，表示客户已经被分配）
                     createdAt: OffsetDateTime = BaseEntity.now,
                     updatedAt: OffsetDateTime = BaseEntity.now,
                     deleted: Boolean = BaseEntity.DEFAULT_DELETED
                   ) extends BaseEntity
