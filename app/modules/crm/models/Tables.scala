package modules.crm.models

import core.BaseTable
import slick.jdbc.MySQLProfile.api._

import java.time.OffsetDateTime

/**
 * 客户表映射
 * 将Customer实体映射到数据库表结构
 */
class CustomerTable(tag: Tag) extends Table[Customer](tag, "crm_wj_customers") with BaseTable[Customer] {
  // 基础字段
  def id = column[Option[String]]("id", <PERSON><PERSON>)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // Customer特有字段
  def name = column[String]("name_", O.Length(100))
  def linkMan = column[String]("link_man", O.Length(100))
  def position = column[Option[String]]("position")
  def country = column[Option[String]]("country")
  def address = column[Option[String]]("address")
  def phone = column[Option[String]]("phone")
  def mobile = column[Option[String]]("mobile")
  def website = column[Option[String]]("website")
  def email = column[Option[String]]("email")
  def source = column[String]("source", O.Length(50))
  def type_ = column[String]("type_", O.Length(50))
  def description = column[Option[String]]("description")
  def salesmanId = column[Option[String]]("salesman_id")
  def assignedAt = column[Option[OffsetDateTime]]("assigned_at")

  // 映射方法，将行数据转换为Customer实体
  def * = (
    id, name, linkMan, position, country, address, phone, mobile, website, email,
    source, type_, description, salesmanId, assignedAt, createdAt, updatedAt, deleted
  ) <> ((Customer.apply _).tupled, Customer.unapply)

  // 索引
  def nameIdx = index("idx_customer_name", name, unique = false)
  def sourceIdx = index("idx_customer_source", source, unique = false)
  def typeIdx = index("idx_customer_type", type_, unique = false)
  def salesmanIdIdx = index("idx_customer_salesman_id", salesmanId, unique = false)
}

/**
 * 客户进度表映射
 * 将Progress实体映射到数据库表结构
 */
class ProgressTable(tag: Tag) extends Table[Progress](tag, "crm_wj_progress") with BaseTable[Progress] {
  // 基础字段
  def id = column[Option[String]]("id", O.PrimaryKey)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // Progress特有字段
  def customerId = column[String]("customer_id")
  def salesmanId = column[String]("salesman_id")
  def phase = column[String]("phase", O.Length(50))
  def plan = column[String]("plan", O.Length(255))
  def remark = column[Option[String]]("remark")
  def status = column[String]("status", O.Length(50))

  // 映射方法，将行数据转换为Progress实体
  def * = (
    id, customerId, salesmanId, phase, plan, remark, status, createdAt, updatedAt, deleted
  ) <> ((Progress.apply _).tupled, Progress.unapply)

  // 索引
  def customerIdIdx = index("idx_progress_customer_id", customerId, unique = false)
  def phaseIdx = index("idx_progress_phase", phase, unique = false)
  def statusIdx = index("idx_progress_status", status, unique = false)
}

