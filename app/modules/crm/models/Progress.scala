package modules.crm.models

import core.models.BaseEntity

import java.time.OffsetDateTime

/**
 * 表示客户进度的各个阶段和状态。
 *
 * @param customerId 客户的唯一标识符。
 * @param salesmanId 销售ID。
 * @param phase      当前阶段。
 * @param plan       进度描述。
 * @param remark     备注或附加信息。
 * @param status     当前状态。
 */
case class Progress(
                     var id: Option[String] = None,
                     customerId: String,
                     salesmanId: String,
                     phase: String,
                     plan: String,
                     remark: Option[String] = None,
                     status: String,
                     createdAt: OffsetDateTime = BaseEntity.now,
                     updatedAt: OffsetDateTime = BaseEntity.now,
                     deleted: Boolean = BaseEntity.DEFAULT_DELETED
                   ) extends BaseEntity
