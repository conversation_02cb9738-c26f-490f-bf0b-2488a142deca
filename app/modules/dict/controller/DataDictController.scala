package modules.dict.controller

import core.BaseController
import core.auth.SecuredAction
import core.models.ApiResponse
import jakarta.inject.Inject
import modules.dict.models.DataDictTable
import modules.dict.models.DataDict
import modules.dict.repository.DataDictRepository
import modules.dict.service.DataDictService
import play.api.http.ContentTypes
import play.api.i18n.MessagesApi
import play.api.mvc.{Action, AnyContent, ControllerComponents}

import scala.concurrent.{ExecutionContext, Future}

class DataDictController @Inject()(
                                    override val service: DataDictService,
                                    securedAction: SecuredAction,
                                    override val cc: ControllerComponents,
                                    override val messagesApi: MessagesApi
                                  )(implicit val ec: ExecutionContext)
  extends BaseController[DataDict, DataDictTable, DataDictRepository, DataDictService](service, cc) {


  def dicts(): Action[AnyContent] = Action.async {
    service.dicts().map(
      data => {
        val result = data.filter(d => d.dictId.isEmpty).map(
          dict => DataDict.SddTree(
            id = dict.id.get.toString,
            name = dict.dictName,
            code = dict.code,
            createTime = dict.createdAt.toInstant.toEpochMilli,
            remark = dict.remark
          )

        )
        Ok(ApiResponse.success(result).toJson).as(ContentTypes.JSON)
      }
    )
  }

  def dictAdd(): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
      val title = (json \ "title").as[String]
      val name = (json \ "name").as[String]
      val code = (json \ "code").as[String]
      val remark = (json \ "remark").as[String]

      val dict = DataDict(
        code = code,
        dictName = name,
        dictValue = code,
        dictLabel = name,
        remark = if (remark.isEmpty) {
          None
        } else {
          Some(remark)
        },
        sort = 0,
        status = 1
      )
      service.save(dict).map {
        dict =>
          Ok(ApiResponse.success(dict).toJson).as(ContentTypes.JSON)
      }

    }.getOrElse {
      Future.successful(BadRequest(
        ApiResponse.error[DataDict](400, "无效的请求数据", Seq("请求体必须是 JSON 格式")).toJson).as(ContentTypes.JSON))
    }
  }

  def dictUpdate(): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
      val id = (json \ "id").asOpt[String]
      val name = (json \ "name").as[String]
      val code = (json \ "code").as[String]
      val remark = (json \ "remark").as[String]

      val dict = DataDict(
        id = id,
        code = code,
        dictName = name,
        dictValue = code,
        dictLabel = name,
        remark = if (remark.isEmpty) {
          None
        } else {
          Some(remark)
        },
        sort = 0,
        status = 1
      )
      service.update(dict).map {
        dict =>
          Ok(ApiResponse.success(dict).toJson).as(ContentTypes.JSON)
      }

    }.getOrElse {
      Future.successful(BadRequest(
        ApiResponse.error[DataDict](400, "无效的请求数据", Seq("请求体必须是 JSON 格式")).toJson).as(ContentTypes.JSON))
    }
  }

  def dictDel(id: String): Action[AnyContent] = Action.async {
    service.delete(id).map {
      _ => Ok(ApiResponse.success("删除成功").toJson).as(ContentTypes.JSON)
    }
  }

  def dictDetails(page: Int, pageSize: Int): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
      val dictId = (json \ "dictId").asOpt[String]
      service.dictDetails(page, pageSize, dictId).map { case (entities, total) =>
        val resultMap = Map(
          "list" -> entities.map(dict => DataDict.SddNode(
            id = dict.id.get,
            color = dict.color.getOrElse(""),
            dictId = dict.dictId.get,
            name = dict.dictName,
            label = dict.dictLabel,
            value = dict.dictValue,
            sort = dict.sort,
            status = dict.status,
            remark = dict.remark,
            metadata = dict.metadata,
            createTime = dict.createdAt.toInstant.toEpochMilli
          )),
          "currentPage" -> page,
          "pageSize" -> pageSize,
          "total" -> total,
          "totalPages" -> math.ceil(total.toDouble / pageSize).toInt
        )
        Ok(ApiResponse.success(resultMap).toJson).as(ContentTypes.JSON)
      }
    }.getOrElse(Future.successful(BadRequest("Invalid JSON")))
  }


  def dictDetailAdd(): Action[AnyContent] = Action.async { implicit request =>
    request.body.asJson.map { json =>
      val dictId = (json \ "dictId").asOpt[String]
      val color = (json \ "color").asOpt[String]
      val label = (json \ "label").as[String]
      val value = (json \ "value").as[String]
      val sort = (json \ "sort").as[Int]
      val status = (json \ "status").as[Int]
      val remark = (json \ "remark").asOpt[String]
      val metadata = (json \ "metadata").asOpt[String]

      val dictDetail = DataDict(
        code = value,
        dictId = dictId,
        dictName = label,
        dictLabel = label,
        dictValue = value,
        sort = sort,
        status = status,
        remark = remark,
        metadata = metadata,
        color = color
      )

      service.save(dictDetail).map {
        dict =>
          Ok(ApiResponse.success(dict).toJson).as(ContentTypes.JSON)
      }

    }.getOrElse {
      Future.successful(BadRequest(
        ApiResponse.error[DataDict](400, "无效的请求数据", Seq("请求体必须是 JSON 格式")).toJson).as(ContentTypes.JSON))
    }
  }

  def dictDetailUpdate(): Action[AnyContent] = Action.async {
    implicit request =>
      request.body.asJson.map { json =>
        val id = (json \ "id").asOpt[String]
        val dictId = (json \ "dictId").asOpt[String]
        val color = (json \ "color").asOpt[String]
        val label = (json \ "label").as[String]
        val value = (json \ "value").as[String]
        val sort = (json \ "sort").as[Int]
        val status = (json \ "status").as[Int]
        val remark = (json \ "remark").asOpt[String]
        val metadata = (json \ "metadata").asOpt[String]

        val dictDetail = DataDict(
          id = id,
          code = value,
          dictId =dictId,
          dictName = label,
          dictLabel = label,
          dictValue = value,
          sort = sort,
          status = status,
          remark = remark,
          metadata = metadata,
          color = color
        )

        service.update(dictDetail).map {
          dict =>
            Ok(ApiResponse.success(dict).toJson).as(ContentTypes.JSON)
        }

      }.getOrElse {
        Future.successful(BadRequest(
          ApiResponse.error[DataDict](400, "无效的请求数据", Seq("请求体必须是 JSON 格式")).toJson).as(ContentTypes.JSON))
      }
  }

  def dictDetailDel(id: String): Action[AnyContent] = Action.async {
    service.delete(id).map {
      _ => Ok(ApiResponse.success("删除成功").toJson).as(ContentTypes.JSON)
    }
  }

  def selectOptions(dict: String): Action[AnyContent] = Action.async {
    service.selectOptions(1, 1000, Some(dict)).map { case (entities, total) =>
      val options = entities.map(dict => Map("label" -> dict.dictLabel, "value" -> dict.dictValue))
      Ok(ApiResponse.success(options).toJson).as(ContentTypes.JSON)
    }
  }
}
