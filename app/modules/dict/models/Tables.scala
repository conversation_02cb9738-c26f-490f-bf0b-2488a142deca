package modules.dict.models

import core.BaseTable
import slick.jdbc.MySQLProfile.api._
import slick.lifted.{ProvenShape, Tag}

import java.time.OffsetDateTime

/**
 * 数据字典表定义
 *
 * @param tag 表标签
 */
class DataDictTable(tag: Tag) extends Table[DataDict](tag, "sys_dictionaries") with BaseTable[DataDict] {

  // 基础字段
  def id = column[Option[String]]("id", O<PERSON>Key)
  def createdAt = column[OffsetDateTime]("created_at")
  def updatedAt = column[OffsetDateTime]("updated_at")
  def deleted = column[Boolean]("deleted")

  // 数据字典字段
  def code = column[String]("code")
  def dictId = column[Option[String]]("dict_id")
  def dictName = column[String]("dict_name")
  def dictType = column[Option[String]]("dict_type")
  def dictValue = column[String]("dict_value")
  def dictLabel = column[String]("dict_label")
  def color = column[Option[String]]("color")
  def metadata = column[Option[String]]("metadata")
  def remark = column[Option[String]]("remark")
  def sort = column[Long]("sort")
  def status = column[Long]("status")

  // 映射方法，将行数据转换为DataDict实体
  def * : ProvenShape[DataDict] = (
    id, code, dictId, dictName, dictType, dictValue, dictLabel, color, metadata, remark, sort, status, createdAt, updatedAt, deleted
  ) <> ((DataDict.apply _).tupled, DataDict.unapply)

  // 索引
  def codeIndex = index("idx_dict_code", code, unique = false)
  def dictIdIndex = index("idx_dict_parent_id", dictId, unique = false)
  def dictTypeIndex = index("idx_dict_type", dictType, unique = false)
}
