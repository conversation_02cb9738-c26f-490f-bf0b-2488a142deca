package modules.dict.models

import core.models.BaseEntity

import java.time.OffsetDateTime

case class DataDict(
                     var id: Option[String] = None,
                     code: String,
                     dictId: Option[String] = None,
                     dictName: String,
                     dictType: Option[String] = None,
                     dictValue: String,
                     dictLabel: String,
                     color:  Option[String] = None,
                     metadata: Option[String] = None,
                     remark: Option[String] = None,
                     sort: Long,
                     status: Long,
                     createdAt: OffsetDateTime = BaseEntity.now,
                     updatedAt: OffsetDateTime = BaseEntity.now,
                     deleted: Boolean = BaseEntity.DEFAULT_DELETED
                   ) extends BaseEntity


object DataDict {

  case class SddTree(
                      id: String,
                      name: String,
                      code: String,
                      createTime: Long,
                      remark: Option[String] = None
                    )

  case class SddNode(
                      id: String,
                      color: String,
                      dictId: String,
                      name: String,
                      label: String,
                      value: String,
                      sort: Long,
                      status: Long,
                      remark: Option[String] = None,
                      metadata: Option[String] = None,
                      createTime: Long
                    )
}
