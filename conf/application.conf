# https://www.playframework.com/documentation/latest/Configuration

# Default database configuration
# Database migration scripts are located in conf/evolutions/default/*.sql
slick.dbs.default {
  profile = "slick.jdbc.MySQLProfile$"
  db {
    driver = "com.mysql.cj.jdbc.Driver"
    url = "jdbc:mysql://************:3306/crm?useSSL=false&serverTimezone=UTC&useUnicode=true&characterEncoding=utf8"
    user = "root"
    password = "8PP?nBY.k)TN63Kdvxnp"
    numThreads = 10
    maxConnections = 10
    minConnections = 1
    connectionTimeout = 5000
    # SQL 日志配置
    logSql = true
    logStatements = true
  }
}

# Application secret
play.http.secret.key="A1B5F6G7H,8I9JP2C3D1L2#M3N4O56Q7_R8S4E0K9T0"

# HTTP Server configuration
play.server.http.port=8080

# 全局HTTP配置
play.http.actionComposition.executeActionCreatorDirectly = true
play.http.errorHandler = "core.ErrorHandler"
play.http.parser.maxMemoryBuffer = 10M
play.http.parser.maxDiskBuffer = 100M

# Assets configuration
play.assets {
  path = "/public"
  urlPrefix = "/assets"
}

# I18n configuration
play.i18n {
  langs = [ "zh", "en" ]
  default = "zh"
}

# Add to enable evolutions
play.evolutions.enabled=true
play.evolutions.autoApply=true

# JWT配置
jwt {
  # 密钥，用于签名JWT
  secret = "PlayNebulaSecretKey123!@#"
  # 访问令牌过期时间（秒）- 30分钟
  access.expiry = 1800
  # 刷新令牌过期时间（秒）- 7天
  refresh.expiry = 604800
}

# 绑定模块注册
play.modules.enabled += "modules.ModuleRegistry"
play.modules.enabled += "modules.file.Module"
play.modules.enabled += "modules.code.Module"

# 全局过滤器
play.http.filters = "filters.Filters"

# 允许的跨域资源来源
# CORS Filter Configuration
play.filters.cors {
  # Allow requests from any origin
  allowedOrigins = ["*"]
  # Allow specific HTTP methods
  allowedHttpMethods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  # Allow specific HTTP headers
  allowedHttpHeaders = ["Accept", "Content-Type", "Origin", "X-Requested-With"]
  # Expose specific HTTP headers
  exposedHeaders = []
  # Set to true if you want to allow credentials
  supportsCredentials = false
  # Set the preflight request cache duration
  preflightMaxAge = 3 days
}


# Slick 日志配置
play.logger.level = INFO
logger.slick = DEBUG
logger.slick.jdbc.JdbcBackend.statement = DEBUG
