# 数据字典模块 API 接口
GET           /select-options        modules.dict.controller.DataDictController.selectOptions(dict: String)
#
GET           /tree                  modules.dict.controller.DataDictController.dicts()
POST          /tree-add              modules.dict.controller.DataDictController.dictAdd()
POST          /tree-update           modules.dict.controller.DataDictController.dictUpdate()
DELETE        /tree/:id              modules.dict.controller.DataDictController.dictDel(id: String)
POST          /detail                modules.dict.controller.DataDictController.dictDetails(page: Int ?= 1, pageSize: Int ?= 10)
POST          /detail-add            modules.dict.controller.DataDictController.dictDetailAdd()
POST          /detail-update         modules.dict.controller.DataDictController.dictDetailUpdate()
DELETE        /detail/:id            modules.dict.controller.DataDictController.dictDetailDel(id: String)
