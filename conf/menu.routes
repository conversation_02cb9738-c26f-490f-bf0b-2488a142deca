# Menu模块API接口
GET           /async-routes        modules.menu.controller.MenuController.asyncRoutes()
GET           /role-menu           modules.menu.controller.MenuController.roleMenus()
POST          /                    modules.menu.controller.MenuController.menu()
POST          /add                 modules.menu.controller.MenuController.add()
POST          /update              modules.menu.controller.MenuController.update()
DELETE        /:id                 modules.menu.controller.MenuController.delete(id: String)
