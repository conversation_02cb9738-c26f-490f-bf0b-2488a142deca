# 角色权限模块API接口
POST          /role                      modules.authority.controller.AuthorityController.roles(page: Int ?= 1, pageSize: Int ?= 10)
GET           /role-all                  modules.authority.controller.AuthorityController.roles2()
POST          /role-menu-save            modules.authority.controller.AuthorityController.roleMenuSave()
GET           /role-menu-ids             modules.authority.controller.AuthorityController.roleMenuIds(id: String)
POST          /role-add                  modules.authority.controller.AuthorityController.roleAdd()
POST          /role-update               modules.authority.controller.AuthorityController.roleUpdate()
DELETE        /role/:id                  modules.authority.controller.AuthorityController.roleDelete(id: String)
POST          /roles2user                modules.authority.controller.AuthorityController.roles2user()
GET           /user-roles/:userid        modules.authority.controller.AuthorityController.userRoles(userid: String)
