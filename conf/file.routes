# 文件管理模块 API 接口
GET           /download                 modules.file.controller.FileController.downloadFile(fileRef: String, attachment: Boolean ?= false)
POST          /upload                   modules.file.controller.FileController.multipartFileUpload()
POST          /uploads                  modules.file.controller.FileController.multipartFilesUpload()
DELETE        /delete                   modules.file.controller.FileController.removeFile(fileRef: String)