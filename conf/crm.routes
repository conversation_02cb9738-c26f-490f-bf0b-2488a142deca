# CRM客户管理模块路由配置

# 客户相关路由
GET           /customers/:id                 modules.crm.controller.CustomerController.findById(id: String)
POST          /customers/batch-import        modules.crm.controller.CustomerController.batchImport()
POST          /customers                     modules.crm.controller.CustomerController.add()
PUT           /customers                     modules.crm.controller.CustomerController.update()
DELETE        /customers/:id                 modules.crm.controller.CustomerController.delete(id: String)
POST          /customers/deletes             modules.crm.controller.CustomerController.deletes()
POST          /customers/search              modules.crm.controller.CustomerController.search(page: Int ?= 1, pageSize: Int ?= 10)
POST          /customers-unassigned          modules.crm.controller.CustomerController.unassigned(page: Int ?= 1, pageSize: Int ?= 10)
POST          /customers-salesman/:id        modules.crm.controller.CustomerController.bySalesman(id: String, page: Int ?= 1, pageSize: Int ?= 10)
POST          /customers-assign              modules.crm.controller.CustomerController.assignSalesman()
POST          /customers/set-status          modules.crm.controller.CustomerController.setCustomerStatus()

# 客户进度相关路由
POST          /progress                      modules.crm.controller.ProgressController.progress(salesmanId: String, page: Int ?= 1, pageSize: Int ?= 10)
GET           /progress/:id                  modules.crm.controller.ProgressController.findById(id: String)
POST          /progress-add                  modules.crm.controller.ProgressController.add()
PUT           /progress-update               modules.crm.controller.ProgressController.update()
DELETE        /progress/:id                  modules.crm.controller.ProgressController.delete(id: String)
POST          /progress/deletes              modules.crm.controller.ProgressController.deletes()

