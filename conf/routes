# Routes
# This file defines all application routes (Higher priority routes first)
# https://www.playframework.com/documentation/latest/ScalaRouting
# ~~~~

# 首页
GET         /                        controllers.HomeController.index()

# Map static resources from the /public folder to the /assets URL path
GET         /assets/*file            controllers.Assets.versioned(path="/public", file: Asset)

# 认证相关路由
POST        /api/auth/login          controllers.AuthController.authenticate()
POST        /api/auth/refresh        controllers.AuthController.refreshToken()
POST        /api/auth/logout         controllers.AuthController.logout()


->          /api/authority           authority.Routes
->          /api/dict                dict.Routes
->          /file                    file.Routes
->          /api/menu                menu.Routes
->          /api/org                 org.Routes
->          /api/crm2                crm.Routes
