<?xml version="1.0" encoding="UTF-8" ?>

<!-- https://www.playframework.com/documentation/latest/SettingsLogger -->

<!DOCTYPE configuration>

<configuration>
  <import class="ch.qos.logback.classic.encoder.PatternLayoutEncoder"/>
  <import class="ch.qos.logback.classic.AsyncAppender"/>
  <import class="ch.qos.logback.core.FileAppender"/>
  <import class="ch.qos.logback.core.ConsoleAppender"/>

  <appender name="FILE" class="FileAppender">
    <file>${application.home:-.}/logs/application.log</file>
    <encoder class="PatternLayoutEncoder">
      <charset>UTF-8</charset>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %highlight(%-5level) %cyan(%logger{36}) %magenta(%X{pekkoSource}) %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="STDOUT" class="ConsoleAppender">
    <!--
         On Windows, enabling Jansi is recommended to benefit from color code interpretation on DOS command prompts,
         which otherwise risk being sent ANSI escape sequences that they cannot interpret.
         See https://logback.qos.ch/manual/layouts.html#coloring
    -->
    <!-- <withJansi>true</withJansi> -->
    <encoder class="PatternLayoutEncoder">
      <charset>UTF-8</charset>
      <pattern>%d{yyyy-MM-dd HH:mm:ss} %highlight(%-5level) %cyan(%logger{36}) %magenta(%X{pekkoSource}) %msg%n</pattern>
    </encoder>
  </appender>

  <logger name="slick.jdbc.JdbcBackend.statement" level="DEBUG"/>
  <logger name="slick.jdbc.JdbcBackend.parameter" level="DEBUG"/>
  <logger name="slick.jdbc.StatementInvoker.result" level="DEBUG"/>
  <logger name="slick" level="INFO"/>

  <appender name="ASYNCFILE" class="AsyncAppender">
    <appender-ref ref="FILE"/>
  </appender>

  <appender name="ASYNCSTDOUT" class="AsyncAppender">
    <appender-ref ref="STDOUT"/>
  </appender>

  <logger name="play" level="INFO"/>
  <logger name="application" level="DEBUG"/>

  <root level="WARN">
    <appender-ref ref="ASYNCFILE"/>
    <appender-ref ref="ASYNCSTDOUT"/>
  </root>

</configuration>
