# 组织架构模块API接口
GET           /user-info             modules.org.controller.OrganizationController.userInfo()
POST          /dept                  modules.org.controller.OrganizationController.deptList()
POST          /dept-add              modules.org.controller.OrganizationController.deptAdd()
POST          /dept-update           modules.org.controller.OrganizationController.deptUpdate()
DELETE        /dept/:id              modules.org.controller.OrganizationController.deptDelete(id: String)
POST          /user                  modules.org.controller.OrganizationController.userList(page: Int ?= 1, pageSize: Int ?= 10)
POST          /user-add              modules.org.controller.OrganizationController.userAdd()
POST          /user-update           modules.org.controller.OrganizationController.userUpdate()
POST          /user-avata            modules.org.controller.OrganizationController.userSetAvata()
POST          /user-reset-pwd        modules.org.controller.OrganizationController.resetPassword()
DELETE        /user-batch            modules.org.controller.OrganizationController.userDeletes()
DELETE        /user/:id              modules.org.controller.OrganizationController.userDelete(id: String)
