-- Dictionary module tables structure

-- !Ups

-- ----------------------------
-- 数据字典表
-- 存储系统数据字典信息，支持层次结构
-- ----------------------------
CREATE TABLE IF NOT EXISTS sys_dictionaries (
    id VARCHAR(64) PRIMARY KEY COMMENT '字典ID',
    code VARCHAR(100) NOT NULL COMMENT '字典编码，唯一标识符',
    dict_id VARCHAR(64) COMMENT '父级字典ID，用于构建树形结构，顶级节点为NULL',
    dict_name VARCHAR(100) NOT NULL COMMENT '字典名称',
    dict_type VARCHAR(100) COMMENT '字典类型，用于分类',
    dict_value VARCHAR(255) NOT NULL COMMENT '字典值，实际存储的值',
    dict_label VARCHAR(255) NOT NULL COMMENT '字典标签，显示名称',
    color VARCHAR(50) DEFAULT '#409EFF' COMMENT '显示颜色，用于前端展示',
    metadata TEXT COMMENT '元数据，JSON格式存储额外信息',
    remark VARCHAR(255) COMMENT '备注说明',
    sort BIGINT NOT NULL DEFAULT 0 COMMENT '排序号，值越小越靠前',
    status BIGINT NOT NULL DEFAULT 1 COMMENT '状态（1正常 0停用）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    INDEX idx_dict_code (code) COMMENT '字典编码索引',
    INDEX idx_dict_parent_id (dict_id) COMMENT '父级字典ID索引',
    INDEX idx_dict_type (dict_type) COMMENT '字典类型索引'
) COMMENT='数据字典表';

-- !Downs

DROP TABLE IF EXISTS sys_dictionaries; 