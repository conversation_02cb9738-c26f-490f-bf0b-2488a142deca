-- Org module tables structure

-- !Ups

-- 客户表创建
CREATE TABLE IF NOT EXISTS `crm_wj_customers` (
                                                  id VARCHAR(64) PRIMARY KEY COMMENT 'ID',
    `name_` VARCHAR(100) NOT NULL COMMENT '客户名称',
    `link_man` VARCHAR(100)  COMMENT '联系人',
    `position` VARCHAR(255) DEFAULT NULL COMMENT '职位',
    `country` VARCHAR(255) DEFAULT NULL COMMENT '国家',
    `address` VARCHAR(255) DEFAULT NULL COMMENT '地址',
    `phone` VARCHAR(255) DEFAULT NULL COMMENT '电话',
    `mobile` VARCHAR(255) DEFAULT NULL COMMENT '手机',
    `website` VARCHAR(255) DEFAULT NULL COMMENT '网站',
    `email` VARCHAR(255) DEFAULT NULL COMMENT '邮箱',
    `source` VARCHAR(255) NOT NULL COMMENT '来源',
    `type_` VARCHAR(255) NOT NULL COMMENT '类型',
    `description` TEXT DEFAULT NULL COMMENT '描述',
    `salesman_id` VARCHAR(64) DEFAULT NULL COMMENT '销售人员ID',
    `assigned_at` DATETIME DEFAULT NULL COMMENT '分配时间',
    `created_at` DATETIME NOT NULL COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX `idx_customer_name` (`name_`),
    INDEX `idx_customer_source` (`source`),
    INDEX `idx_customer_type` (`type_`),
    INDEX `idx_customer_salesman_id` (`salesman_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户表';

-- 客户进度表创建
CREATE TABLE IF NOT EXISTS `crm_wj_progress` (
                                                 id VARCHAR(64) PRIMARY KEY COMMENT 'ID',
    `customer_id` VARCHAR(64) NOT NULL COMMENT '客户ID',
    `salesman_id` VARCHAR(64)  COMMENT '销售ID',
    `phase` VARCHAR(255) NOT NULL COMMENT '阶段',
    `plan` VARCHAR(255) NOT NULL COMMENT '计划',
    `remark` TEXT DEFAULT NULL COMMENT '备注',
    `status` VARCHAR(50) NOT NULL COMMENT '状态',
    `created_at` DATETIME NOT NULL COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX `idx_progress_customer_id` (`customer_id`),
    INDEX `idx_progress_phase` (`phase`),
    INDEX `idx_progress_status` (`status`),
    CONSTRAINT `fk_progress_customer` FOREIGN KEY (`customer_id`) REFERENCES `crm_wj_customers` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户进度表';


-- !Downs

DROP TABLE IF EXISTS crm_wj_progress;
DROP TABLE IF EXISTS crm_wj_customers;
