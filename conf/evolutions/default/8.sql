-- Org module tables structure

-- !Ups

-- ----------------------------
-- 添加父级字典项
-- ----------------------------
INSERT INTO sys_dictionaries (id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
-- 系统状态
('1', 'sys_normal_disable', '系统状态', 'system', '0', '系统状态', '#409EFF', 1, 1),
-- 用户性别
('2', 'sys_user_sex', '用户性别', 'system', '0', '用户性别', '#409EFF', 2, 1),
-- 菜单类型
('3', 'sys_menu_type', '菜单类型', 'system', '0', '菜单类型', '#409EFF', 3, 1),
-- 数据范围
('4', 'sys_data_scope', '数据范围', 'system', '0', '数据范围', '#409EFF', 4, 1),
-- 系统是否
('5', 'sys_yes_no', '系统是否', 'system', '0', '系统是否', '#409EFF', 5, 1),
-- 任务状态
('6', 'sys_job_status', '任务状态', 'system', '0', '任务状态', '#409EFF', 6, 1),
-- 任务分组
('7', 'sys_job_group', '任务分组', 'system', '0', '任务分组', '#409EFF', 7, 1),
-- 通知类型
('8', 'sys_notice_type', '通知类型', 'system', '0', '通知类型', '#409EFF', 8, 1),
-- 通知状态
('9', 'sys_notice_status', '通知状态', 'system', '0', '通知状态', '#409EFF', 9, 1),
-- 操作类型
('10', 'sys_oper_type', '操作类型', 'system', '0', '操作类型', '#409EFF', 10, 1),
-- 系统状态
('11', 'sys_common_status', '系统状态', 'system', '0', '系统状态', '#409EFF', 11, 1);

-- ----------------------------
-- 添加子级字典项，使用父级的ID
-- ----------------------------

-- 系统状态子项
INSERT INTO sys_dictionaries (id, dict_id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
    ('12', '1', 'sys_normal_disable_normal', '正常', 'system', '1', '正常', '#67C23A', 1, 1),
    ('13', '1', 'sys_normal_disable_disable', '停用', 'system', '0', '停用', '#F56C6C', 2, 1);

-- 用户性别子项
INSERT INTO sys_dictionaries (id, dict_id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
    ('14', '2', 'sys_user_sex_male', '男', 'user', '0', '男', '#409EFF', 1, 1),
    ('15', '2', 'sys_user_sex_female', '女', 'user', '1', '女', '#F56C6C', 2, 1),
    ('16', '2', 'sys_user_sex_unknown', '未知', 'user', '2', '未知', '#909399', 3, 1);

-- 菜单类型子项
INSERT INTO sys_dictionaries (id, dict_id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
    ('17', '3', 'sys_menu_type_menu', '菜单', 'menu', '0', '菜单', '#409EFF', 1, 1),
    ('18', '3', 'sys_menu_type_iframe', 'iframe', 'menu', '1', 'iframe', '#409EFF', 2, 1),
    ('19', '3', 'sys_menu_type_external', '外链', 'menu', '2', '外链', '#409EFF', 3, 1),
    ('20', '3', 'sys_menu_type_button', '按钮', 'menu', '3', '按钮', '#909399', 4, 1);

-- 系统是否子项
INSERT INTO sys_dictionaries (id, dict_id, code, dict_name, dict_type, dict_value, dict_label, color, sort, status)
VALUES
    ('21', '5', 'sys_yes_no_yes', '是', 'system', '1', '是', '#67C23A', 1, 1),
    ('22', '5', 'sys_yes_no_no', '否', 'system', '0', '否', '#F56C6C', 2, 1);

-- !Downs

-- 删除测试数据
DELETE FROM sys_dictionaries WHERE code LIKE 'sys_%';
