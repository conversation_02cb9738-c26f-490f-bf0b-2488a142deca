-- Org module tables structure

-- !Ups

-- ----------------------------
-- 部门表
-- 存储组织架构中的部门信息
-- ----------------------------
CREATE TABLE IF NOT EXISTS org_departments (
                                               id VARCHAR(64) PRIMARY KEY COMMENT '部门ID',
                                               parent_id VARCHAR(64) COMMENT '父部门ID，顶级部门为null',
                                               name_ VARCHAR(100) NOT NULL COMMENT '部门名称',
    principal VARCHAR(100) NOT NULL COMMENT '部门负责人',
    phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    email VARCHAR(100) NOT NULL COMMENT '联系邮箱',
    sort BIGINT NOT NULL DEFAULT 0 COMMENT '排序号',
    status BIGINT NOT NULL DEFAULT 0 COMMENT '状态（1正常 0停用）',
    remark VARCHAR(255) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    INDEX idx_dept_name (name_) COMMENT '部门名称索引',
    INDEX idx_parent_id (parent_id) COMMENT '父部门ID索引'
    ) COMMENT='部门表';

-- ----------------------------
-- 岗位表
-- 存储组织架构中的岗位信息
-- ----------------------------
CREATE TABLE IF NOT EXISTS org_positions (
                                             id VARCHAR(64) PRIMARY KEY COMMENT '岗位ID',
                                             code VARCHAR(50) NOT NULL COMMENT '岗位编码',
    name_ VARCHAR(100) NOT NULL COMMENT '岗位名称',
    status BIGINT NOT NULL DEFAULT 0 COMMENT '状态（1正常 0停用）',
    sort BIGINT NOT NULL DEFAULT 0 COMMENT '排序号',
    remark VARCHAR(255) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    UNIQUE INDEX idx_position_code (code) COMMENT '岗位编码唯一索引',
    INDEX idx_position_name (name_) COMMENT '岗位名称索引'
    ) COMMENT='岗位表';

-- ----------------------------
-- 用户表
-- 存储系统用户信息
-- ----------------------------
CREATE TABLE IF NOT EXISTS org_users (
                                         id VARCHAR(64) PRIMARY KEY COMMENT '用户ID',
                                         dept_id VARCHAR(64) COMMENT '部门ID',
                                         avatar VARCHAR(510) DEFAULT 'https://i.pravatar.cc/300' COMMENT '用户头像URL',
    username VARCHAR(100) NOT NULL COMMENT '用户名，唯一标识，用于登录',
    sex VARCHAR(20) COMMENT '性别',
    password VARCHAR(510) NOT NULL COMMENT '加密存储的用户密码',
    nickname VARCHAR(100) COMMENT '用户昵称，显示名称',
    email VARCHAR(100) COMMENT '用户邮箱地址',
    phone VARCHAR(100) COMMENT '用户手机号码',
    description VARCHAR(255) COMMENT '用户描述信息',
    status BIGINT  COMMENT '账户是否激活，1=激活，0=禁用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    UNIQUE INDEX idx_username (username) COMMENT '用户名唯一索引',
    INDEX idx_dept_id (dept_id) COMMENT '部门ID索引'
    ) COMMENT='用户表';

-- ----------------------------
-- 用户岗位关联表
-- 存储用户与岗位的多对多关系
-- ----------------------------
CREATE TABLE IF NOT EXISTS org_user_positions (
                                                  id VARCHAR(64) PRIMARY KEY COMMENT '主键ID',
                                                  user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
                                                  position_id VARCHAR(64) NOT NULL COMMENT '岗位ID',
                                                  is_primary BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为主要岗位',
                                                  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

                                                  UNIQUE INDEX idx_user_position (user_id, position_id) COMMENT '用户岗位唯一索引',

    CONSTRAINT fk_user_position_user FOREIGN KEY (user_id) REFERENCES org_users (id) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT fk_user_position_position FOREIGN KEY (position_id) REFERENCES org_positions (id) ON DELETE CASCADE ON UPDATE RESTRICT
    ) COMMENT='用户岗位关联表';

-- !Downs

DROP TABLE IF EXISTS org_user_positions;
DROP TABLE IF EXISTS org_users;
DROP TABLE IF EXISTS org_positions;
DROP TABLE IF EXISTS org_departments; 