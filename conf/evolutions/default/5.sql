-- Menu module tables structure

-- !Ups

-- ----------------------------
-- 菜单表
-- 存储系统导航菜单信息，支持多级嵌套结构
-- ----------------------------
CREATE TABLE IF NOT EXISTS sys_menus (
                                         id VARCHAR(64) PRIMARY KEY COMMENT '菜单ID',
                                         path_ VARCHAR(255) NOT NULL COMMENT '菜单路径，如"/system/user"',
    name_ VARCHAR(100) NOT NULL COMMENT '菜单名称，路由名称，唯一标识',
    menu_type BIGINT COMMENT '菜单类型（0代表菜单、1代表iframe、2代表外链、3代表按钮）',
    redirect VARCHAR(255) COMMENT '重定向路径，配置后点击菜单会跳转到对应路径',
    title VARCHAR(100) NOT NULL COMMENT '菜单标题，显示在界面上的名称',
    icon VARCHAR(100) COMMENT '菜单图标，支持Element Plus或其他图标库',
    extra_icon VARCHAR(100) COMMENT '额外图标，菜单名称右侧的额外图标',
    show_link BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否在菜单中显示，TRUE=显示，FALSE=隐藏',
    show_parent BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否显示父级菜单，TRUE=显示，FALSE=隐藏',
    roles VARCHAR(510) COMMENT '可访问角色列表，多数据用英文逗号隔开',
    auths VARCHAR(510) COMMENT '按钮级别权限设置，多数据用英文逗号隔开',
    keep_alive BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否缓存该路由页面，TRUE=缓存，FALSE=不缓存',
    frame_src VARCHAR(255) COMMENT '内嵌iframe的链接地址',
    frame_loading BOOLEAN NOT NULL DEFAULT FALSE COMMENT '内嵌iframe页面是否开启首次加载动画',
    transition_name VARCHAR(100) COMMENT '当前页面动画名称（第一种模式）',
    enter_transition VARCHAR(100) COMMENT '当前页面进场动画名称（第二种模式）',
    leave_transition VARCHAR(100) COMMENT '当前页面离场动画名称（第二种模式）',
    hidden_tag BOOLEAN NOT NULL DEFAULT FALSE COMMENT '当前菜单名称是否禁止添加到标签页',
    fixed_tag BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否固定菜单，TRUE=固定，FALSE=不固定',
    rank_ BIGINT COMMENT '排序值，值越小越靠前',
    dynamic_level BIGINT COMMENT '显示在标签页的最大数量',
    active_path VARCHAR(255) COMMENT '将某个菜单激活的路径',
    component VARCHAR(255) COMMENT '组件路径，按需加载需要展示的页面',
    parent_id VARCHAR(64) COMMENT '父菜单ID，用于构建菜单树，顶级菜单为NULL',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    INDEX idx_menu_name (name_) COMMENT '菜单名称索引',
    INDEX idx_menu_path (path_) COMMENT '菜单路径索引',
    INDEX idx_menu_parent_id (parent_id) COMMENT '父菜单ID索引，用于查询子菜单'
    ) COMMENT='菜单表';

-- !Downs

DROP TABLE IF EXISTS sys_menus;