-- Org module tables structure

-- !Ups

-- ----------------------------
-- 菜单测试数据
-- ----------------------------
-- 顶级菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, extra_icon, roles, auths, show_link, show_parent, rank_)
VALUES
    ('1', '/tenant', 'TenantManagement', 0, 'menus.pureTenantManagement', 'ri:home-gear-line', 'IF-pure-iconfont-new svg', 'ADMIN', '', FALSE, FALSE, 14),
    ('2', '/system', 'SystemManagement', 0, 'menus.pureSysManagement', 'ri:settings-3-line', NULL, 'ADMIN', '', TRUE, FALSE, 15),
    ('3', '/monitor', 'SystemMonitor', 0, 'menus.pureSysMonitor', 'ep:monitor', NULL, 'ADMIN', '', FALSE, FALSE, 16),
    ('4', '/permission', 'Permission', 0, 'menus.purePermission', 'ep:lollipop', NULL, 'ADMIN,USER', '', FALSE, FALSE, 13),
    ('5', '/iframe', 'IframePage', 0, 'menus.pureExternalPage', 'ri:links-fill', NULL, 'ADMIN,USER', '', FALSE, FALSE, 11),
    ('6', '/tabs', 'TabsManagement', 0, 'menus.pureTabs', 'ri:bookmark-2-line', NULL, 'ADMIN,USER', '', FALSE, FALSE, 17);

-- 租户管理子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('7', '/tenant/list/index', 'TenantList', 0, 'menus.pureTenantList', 'ri:list-check', 'ADMIN', '', TRUE, FALSE, '1', 'tenant/list/index'),
    ('8', '/tenant/package/index', 'TenantPackage', 0, 'menus.pureTenantPackage', 'ri:file-paper-line', 'ADMIN', '', TRUE, FALSE, '1', 'tenant/package/index');

-- 系统管理子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('9', '/system/user/index', 'SystemUser', 0, 'menus.pureUser', 'ri:admin-line', 'ADMIN', 'USER:VIEW', TRUE, FALSE, '2', 'system/user/index'),
    ('10', '/system/role/index', 'SystemRole', 0, 'menus.pureRole', 'ri:admin-fill', 'ADMIN', 'ROLE:VIEW', TRUE, FALSE, '2', 'system/role/index'),
    ('11', '/system/menu/index', 'SystemMenu', 0, 'menus.pureSystemMenu', 'ep:menu', 'ADMIN', 'MENU:VIEW', TRUE, FALSE, '2', 'system/menu/index'),
    ('12', '/system/dept/index', 'SystemDept', 0, 'menus.pureDept', 'ri:git-branch-line', 'ADMIN', 'DEPT:VIEW', TRUE, FALSE, '2', 'system/dept/index'),
    ('13', '/system/dict/index', 'SystemDict', 0, 'menus.pureDict', 'ri:book-2-line', 'ADMIN', '', TRUE, FALSE, '2', 'system/dict/index');

-- 系统监控子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('14', '/monitor/online-user', 'OnlineUser', 0, 'menus.pureOnlineUser', 'ri:user-voice-line', 'ADMIN', 'MONITOR:ONLINE_USERS', TRUE, FALSE, '3', 'monitor/online/index'),
    ('15', '/monitor/login-logs', 'LoginLog', 0, 'menus.pureLoginLog', 'ri:window-line', 'ADMIN', 'MONITOR:LOGIN_LOGS', TRUE, FALSE, '3', 'monitor/logs/login/index'),
    ('16', '/monitor/operation-logs', 'OperationLog', 0, 'menus.pureOperationLog', 'ri:history-fill', 'ADMIN', 'MONITOR:OPERATION_LOGS', TRUE, FALSE, '3', 'monitor/logs/operation/index'),
    ('17', '/monitor/system-logs', 'SystemLog', 0, 'menus.pureSystemLog', 'ri:file-search-line', 'ADMIN', 'MONITOR:SYSTEM_LOGS', TRUE, FALSE, '3', 'monitor/logs/system/index');

-- 权限管理子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('18', '/permission/page/index', 'PermissionPage', 0, 'menus.purePermissionPage', NULL, 'ADMIN,USER', '', TRUE, FALSE, '4', 'permission/page/index'),
    ('19', '/permission/button', 'PermissionButton', 0, 'menus.purePermissionButton', NULL, 'ADMIN,USER', '', TRUE, FALSE, '4', 'permission/button/index');

-- 权限按钮子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('20', '/permission/button/router', 'PermissionButtonRouter', 3, 'menus.purePermissionButtonRouter', NULL, 'ADMIN,USER', 'USER:CREATE,USER:EDIT,USER:DELETE', TRUE, FALSE, '19', 'permission/button/index'),
    ('21', '/permission/button/login', 'PermissionButtonLogin', 3, 'menus.purePermissionButtonLogin', NULL, 'ADMIN,USER', '', TRUE, FALSE, '19', 'permission/button/perms');

-- iframe子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('22', '/iframe/embedded', 'IframeEmbedded', 0, 'menus.pureEmbeddedDoc', NULL, 'ADMIN,USER', '', TRUE, FALSE, '5', 'iframe/embedded/index'),
    ('23', '/iframe/external', 'IframeExternal', 0, 'menus.pureExternalDoc', NULL, 'ADMIN,USER', '', TRUE, FALSE, '5', 'iframe/external/index');

-- 嵌入式iframe子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, frame_src, keep_alive, show_link, show_parent, parent_id, component)
VALUES
    ('24', '/iframe/colorhunt', 'FrameColorHunt', 1, 'menus.pureColorHuntDoc', NULL, 'ADMIN,USER', '', 'https://colorhunt.co/', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('25', '/iframe/uigradients', 'FrameUiGradients', 1, 'menus.pureUiGradients', NULL, 'ADMIN,USER', '', 'https://uigradients.com/', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('26', '/iframe/ep', 'FrameEp', 1, 'menus.pureEpDoc', NULL, 'ADMIN,USER', '', 'https://element-plus.org/zh-CN/', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('27', '/iframe/tailwindcss', 'FrameTailwindcss', 1, 'menus.pureTailwindcssDoc', NULL, 'ADMIN,USER', '', 'https://tailwindcss.com/docs/installation', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('28', '/iframe/vue3', 'FrameVue', 1, 'menus.pureVueDoc', NULL, 'ADMIN,USER', '', 'https://cn.vuejs.org/', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('29', '/iframe/vite', 'FrameVite', 1, 'menus.pureViteDoc', NULL, 'ADMIN,USER', '', 'https://cn.vitejs.dev/', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('30', '/iframe/pinia', 'FramePinia', 1, 'menus.purePiniaDoc', NULL, 'ADMIN,USER', '', 'https://pinia.vuejs.org/zh/index.html', TRUE, TRUE, FALSE, '22', 'iframe/index'),
    ('31', '/iframe/vue-router', 'FrameRouter', 1, 'menus.pureRouterDoc', NULL, 'ADMIN,USER', '', 'https://router.vuejs.org/zh/', TRUE, TRUE, FALSE, '22', 'iframe/index');

-- 外部链接子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('32', '/external', 'https://pure-admin.cn/', 2, 'menus.pureExternalLink', NULL, 'ADMIN,USER', '', TRUE, FALSE, '23', 'external'),
    ('33', '/pureUtilsLink', 'https://pure-admin-utils.netlify.app/', 2, 'menus.pureUtilsLink', NULL, 'ADMIN,USER', '', TRUE, FALSE, '23', 'external');

-- 标签页子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, show_parent, parent_id, component)
VALUES
    ('34', '/tabs/index', 'Tabs', 0, 'menus.pureTabs', NULL, 'ADMIN,USER', '', TRUE, FALSE, '6', 'tabs/index');

-- 标签页子菜单
INSERT INTO sys_menus (id, path_, name_, menu_type, title, icon, roles, auths, show_link, active_path, show_parent, parent_id, component)
VALUES
    ('35', '/tabs/query-detail', 'TabQueryDetail', 0, '', NULL, 'ADMIN,USER', '', FALSE, '/tabs/index', FALSE, '6', 'tabs/query-detail'),
    ('36', '/tabs/params-detail/:id', 'TabParamsDetail', 0, '', NULL, 'ADMIN,USER', '', FALSE, '/tabs/index', FALSE, '6', 'params-detail');

-- !Downs

-- 清除测试数据
DELETE FROM sys_menus;
