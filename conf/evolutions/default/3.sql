-- Authority module tables structure

-- !Ups

-- ----------------------------
-- 角色表
-- 存储系统角色信息
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_roles (
                                          id VARCHAR(64) PRIMARY KEY COMMENT '角色ID',
                                          name_ VARCHAR(100) NOT NULL COMMENT '角色名称',
    code VARCHAR(100) NOT NULL COMMENT '角色编码，唯一标识',
    description VARCHAR(255) COMMENT '角色描述',
    sort_order BIGINT NOT NULL DEFAULT 0 COMMENT '排序号',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否是默认角色',
    status BIGINT DEFAULT TRUE COMMENT '状态，1=启用，0=禁用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    UNIQUE INDEX idx_role_code (code) COMMENT '角色编码唯一索引'
    ) COMMENT='角色表';

-- ----------------------------
-- 权限表
-- 存储系统权限项
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_permissions (
                                                id VARCHAR(64) PRIMARY KEY COMMENT '权限ID',
                                                name_ VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL COMMENT '权限编码，唯一标识',
    type_ VARCHAR(50) NOT NULL COMMENT '权限类型，例如：menu, button, api',
    description VARCHAR(255) COMMENT '权限描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    UNIQUE INDEX idx_permission_code (code) COMMENT '权限编码唯一索引',
    INDEX idx_permission_type (type_) COMMENT '权限类型索引'
    ) COMMENT='权限表';

-- ----------------------------
-- 资源表
-- 存储系统资源信息
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_resources (
                                              id VARCHAR(64) PRIMARY KEY COMMENT '资源ID',
                                              parent_id VARCHAR(64) COMMENT '父资源ID',
                                              menu_id VARCHAR(64) COMMENT '关联的菜单ID',
                                              name_ VARCHAR(100) NOT NULL COMMENT '资源名称',
    code VARCHAR(100) NOT NULL COMMENT '资源编码，唯一标识',
    type_ VARCHAR(50) NOT NULL COMMENT '资源类型，例如：menu, button, api',
    icon VARCHAR(100) COMMENT '资源图标',
    path_ VARCHAR(255) COMMENT '资源路径',
    component VARCHAR(255) COMMENT '组件路径',
    method_ VARCHAR(20) COMMENT 'HTTP方法，用于API资源',
    description VARCHAR(255) COMMENT '资源描述',
    permission_code VARCHAR(100) NOT NULL COMMENT '关联的权限编码',
    sort BIGINT NOT NULL DEFAULT 0 COMMENT '排序号',
    status BOOLEAN NOT NULL DEFAULT TRUE COMMENT '状态，TRUE=启用，FALSE=禁用',
    required_auth BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否需要认证，TRUE=需要，FALSE=不需要',
    metadata TEXT COMMENT '资源元数据，JSON格式',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    UNIQUE INDEX idx_resource_code (code) COMMENT '资源编码唯一索引',
    INDEX idx_resource_type (type_) COMMENT '资源类型索引',
    INDEX idx_resource_parent_id (parent_id) COMMENT '父资源ID索引',
    INDEX idx_resource_menu_id (menu_id) COMMENT '菜单ID索引',
    INDEX idx_resource_permission_code (permission_code) COMMENT '权限编码索引'
    ) COMMENT='资源表';

-- ----------------------------
-- 数据权限规则表
-- 存储数据权限规则
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_data_permission_rules (
                                                          id VARCHAR(64) PRIMARY KEY COMMENT '规则ID',
                                                          name_ VARCHAR(100) NOT NULL COMMENT '规则名称',
    code VARCHAR(100) NOT NULL COMMENT '规则编码，唯一标识',
    entity VARCHAR(100) NOT NULL COMMENT '实体名称，指定规则适用的数据实体',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型，例如：department, user, custom',
    rule_config TEXT COMMENT '规则配置，JSON格式',
    description VARCHAR(255) COMMENT '规则描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除，TRUE=已删除，FALSE=未删除',

    UNIQUE INDEX idx_rule_code (code) COMMENT '规则编码唯一索引',
    INDEX idx_rule_entity (entity) COMMENT '实体名称索引',
    INDEX idx_rule_type (rule_type) COMMENT '规则类型索引'
    ) COMMENT='数据权限规则表';

-- ----------------------------
-- 用户角色关联表
-- 存储用户与角色的多对多关系
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_user_roles (
                                               id VARCHAR(64) PRIMARY KEY COMMENT '主键ID',
                                               user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
                                               role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
                                               created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

                                               INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_role_id (role_id) COMMENT '角色ID索引',
    UNIQUE INDEX idx_user_role (user_id, role_id) COMMENT '用户角色唯一索引'
    ) COMMENT='用户角色关联表';

-- ----------------------------
-- 角色权限关联表
-- 存储角色与权限的多对多关系
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_role_permissions (
                                                     id VARCHAR(64) PRIMARY KEY COMMENT '主键ID',
                                                     role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
                                                     permission_id VARCHAR(64) NOT NULL COMMENT '权限ID',
                                                     created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

                                                     INDEX idx_role_id (role_id) COMMENT '角色ID索引',
    INDEX idx_permission_id (permission_id) COMMENT '权限ID索引',
    UNIQUE INDEX idx_role_permission (role_id, permission_id) COMMENT '角色权限唯一索引'
    ) COMMENT='角色权限关联表';

-- ----------------------------
-- 角色数据权限关联表
-- 存储角色与数据权限规则的多对多关系
-- ----------------------------
CREATE TABLE IF NOT EXISTS auth_role_data_permissions (
                                                          id VARCHAR(64) PRIMARY KEY COMMENT '主键ID',
                                                          role_id VARCHAR(64) NOT NULL COMMENT '角色ID',
                                                          data_permission_id VARCHAR(64) NOT NULL COMMENT '数据权限规则ID',
                                                          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

                                                          INDEX idx_role_id (role_id) COMMENT '角色ID索引',
    INDEX idx_data_permission_id (data_permission_id) COMMENT '数据权限规则ID索引',
    UNIQUE INDEX idx_role_data_permission (role_id, data_permission_id) COMMENT '角色数据权限唯一索引'
    ) COMMENT='角色数据权限关联表';

-- !Downs

DROP TABLE IF EXISTS auth_role_data_permissions;
DROP TABLE IF EXISTS auth_role_permissions;
DROP TABLE IF EXISTS auth_user_roles;
DROP TABLE IF EXISTS auth_data_permission_rules;
DROP TABLE IF EXISTS auth_resources;
DROP TABLE IF EXISTS auth_permissions;
DROP TABLE IF EXISTS auth_roles; 