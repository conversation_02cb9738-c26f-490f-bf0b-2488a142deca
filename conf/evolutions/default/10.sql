-- Org module tables structure

-- !Ups

-- ----------------------------
-- 插入测试数据: 先禁用外键检查，批量导入后再启用
-- ----------------------------
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 客户测试数据
-- ----------------------------
INSERT INTO `crm_wj_customers`
(`id`, `name_`, `position`, `country`, `address`, `phone`, `mobile`, `website`, `email`, `source`, `type_`, `description`, `salesman_id`, `assigned_at`, `created_at`, `updated_at`, `deleted`)
VALUES
-- 已分配销售人员的客户
('1001', '北京星科技有限公司', '10001', '1001', '2001', '3001', '4001', '5001', '6001', '网络推广', 'A类', '7001', '101', '2023-05-10 09:30:00', '2023-05-01 08:30:00', '2023-05-15 10:15:00', 0),
('1002', '上海月辉科技有限公司', '10002', '1002', '2002', '3002', '4002', '5002', '6002', '展会', 'A类', '7002', '102', '2023-05-12 11:20:00', '2023-05-02 14:20:00', '2023-05-16 16:30:00', 0),
('1003', '广州海洋信息技术有限公司', '10003', '1003', '2003', '3003', '4003', '5003', '6003', '老客户推荐', 'B类', '7003', '101', '2023-05-15 15:45:00', '2023-05-03 09:15:00', '2023-05-17 11:30:00', 0),
('1004', '深圳智慧电子有限公司', '10004', '1004', '2004', '3004', '4004', '5004', '6004', '销售电话', 'B类', '7004', '103', '2023-05-18 10:20:00', '2023-05-04 16:40:00', '2023-05-18 14:25:00', 0),
('1005', '杭州云端软件有限公司', '10005', '1005', '2005', '3005', '4005', '5005', '6005', '在线咨询', 'A类', '7005', '102', '2023-05-20 09:10:00', '2023-05-05 11:05:00', '2023-05-22 09:30:00', 0),

-- 未分配销售人员的客户
('1006', '成都创新科技有限公司', '10006', '1006', '2006', '3006', '4006', '5006', '6006', '网络推广', 'C类', '7006', NULL, NULL, '2023-05-06 14:15:00', '2023-05-06 14:15:00', 0),
('1007', '武汉数据分析有限公司', '10007', '1007', '2007', '3007', '4007', '5007', '6007', '展会', 'C类', '7007', NULL, NULL, '2023-05-07 10:40:00', '2023-05-07 10:40:00', 0),
('1008', '西安未来科技有限公司', '10008', '1008', '2008', '3008', '4008', '5008', '6008', '在线咨询', 'B类', '7008', NULL, NULL, '2023-05-08 16:55:00', '2023-05-08 16:55:00', 0),
('1009', '南京智能系统有限公司', '10009', '1009', '2009', '3009', '4009', '5009', '6009', '老客户推荐', 'B类', '7009', NULL, NULL, '2023-05-09 09:25:00', '2023-05-09 09:25:00', 0),
('1010', '重庆网络科技有限公司', '10010', '1010', '2010', '3010', '4010', '5010', '6010', '销售电话', 'C类', '7010', NULL, NULL, '2023-05-10 11:35:00', '2023-05-10 11:35:00', 0);

-- ----------------------------
-- 客户进度测试数据
-- ----------------------------
INSERT INTO `crm_wj_progress`
(`id`, `customer_id`, `phase`, `plan`, `remark`, `status`, `created_at`, `updated_at`, `deleted`)
VALUES
-- 客户1的进度记录
('2001', '1001', '初步接触', '介绍产品功能和优势', '客户对价格比较敏感', '进行中', '2023-05-01 10:30:00', '2023-05-01 10:30:00', 0),
('2002', '1001', '需求分析', '深入了解客户业务需求', '需要定制化解决方案', '已完成', '2023-05-05 14:15:00', '2023-05-05 14:15:00', 0),
('2003', '1001', '方案提交', '提交针对性解决方案和报价', '等待客户反馈', '进行中', '2023-05-12 09:45:00', '2023-05-12 09:45:00', 0),

-- 客户2的进度记录
('2004', '1002', '初步接触', '电话沟通产品信息', '客户表现出较强兴趣', '已完成', '2023-05-02 15:20:00', '2023-05-02 15:20:00', 0),
('2005', '1002', '需求分析', '线下会议详细了解需求', '客户需要高性能解决方案', '已完成', '2023-05-06 10:10:00', '2023-05-06 10:10:00', 0),
('2006', '1002', '方案提交', '提交定制方案', NULL, '已完成', '2023-05-14 11:30:00', '2023-05-14 11:30:00', 0),
('2007', '1002', '商务谈判', '价格和服务条款协商', '客户要求更多优惠', '进行中', '2023-05-20 15:45:00', '2023-05-20 15:45:00', 0),

-- 客户3的进度记录
('2008', '1003', '初步接触', '展会现场交流', '对我们的AI功能非常感兴趣', '已完成', '2023-05-03 16:30:00', '2023-05-03 16:30:00', 0),
('2009', '1003', '需求分析', '远程会议详细沟通', NULL, '已完成', '2023-05-08 09:20:00', '2023-05-08 09:20:00', 0),
('2010', '1003', '方案提交', '提交初步方案', '需要进一步完善技术细节', '进行中', '2023-05-15 14:05:00', '2023-05-15 14:05:00', 0),

-- 客户4的进度记录
('2011', '1004', '初步接触', '电话介绍产品', '客户时间有限，简要介绍', '已完成', '2023-05-04 11:15:00', '2023-05-04 11:15:00', 0),
('2012', '1004', '需求分析', '线下拜访详细交流', '客户重点关注安全性', '已完成', '2023-05-10 10:35:00', '2023-05-10 10:35:00', 0),
('2013', '1004', '方案提交', '提交安全加强型方案', NULL, '已完成', '2023-05-18 15:40:00', '2023-05-18 15:40:00', 0),
('2014', '1004', '商务谈判', '商讨合同细节', '客户对付款条件有疑问', '进行中', '2023-05-22 09:55:00', '2023-05-22 09:55:00', 0),

-- 客户5的进度记录
('2015', '1005', '初步接触', '在线会议介绍', '客户团队多人参与', '已完成', '2023-05-05 14:25:00', '2023-05-05 14:25:00', 0),
('2016', '1005', '需求分析', '深入了解业务流程', '需要系统集成方案', '已完成', '2023-05-12 11:20:00', '2023-05-12 11:20:00', 0),
('2017', '1005', '方案提交', '提交集成解决方案', '方案包含多个模块', '已完成', '2023-05-19 10:15:00', '2023-05-19 10:15:00', 0),
('2018', '1005', '商务谈判', '讨论实施周期和价格', NULL, '进行中', '2023-05-23 15:30:00', '2023-05-23 15:30:00', 0);

-- ----------------------------
-- 重新启用外键检查
-- ----------------------------
SET FOREIGN_KEY_CHECKS = 1;

-- !Downs

-- 清除测试数据
DELETE FROM crm_wj_customers;
DELETE FROM crm_wj_progress;
