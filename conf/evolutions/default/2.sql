-- Org module tables structure

-- !Ups

-- ----------------------------
-- 部门测试数据
-- ----------------------------
INSERT INTO org_departments (id, parent_id, name_, principal, phone, email, sort, status, remark)
VALUES
    ('1', NULL, '总公司', '张三', '13800138000', 'zhang<PERSON>@example.com', 1, 1, '总公司'),
    ('2', '1', '研发部', '李四', '13800138001', '<EMAIL>', 1, 1, '负责系统研发'),
    ('3', '1', '市场部', '王五', '13800138002', '<EMAIL>', 2, 1, '负责市场推广'),
    ('4', '1', '财务部', '赵六', '13800138003', '<EMAIL>', 3, 1, '负责财务管理'),
    ('5', '2', '前端组', '孙七', '13800138004', 'sun<PERSON>@example.com', 1, 1, '负责前端开发'),
    ('6', '2', '后端组', '周八', '13800138005', '<EMAIL>', 2, 1, '负责后端开发'),
    ('7', '2', '测试组', '吴九', '13800138006', '<EMAIL>', 3, 1, '负责系统测试');

-- ----------------------------
-- 岗位测试数据
-- ----------------------------
INSERT INTO org_positions (id, code, name_, status, sort, remark)
VALUES
    ('1', 'CEO', '董事长', 1, 1, '公司最高领导职位'),
    ('2', 'CTO', '技术总监', 1, 2, '负责公司技术方向'),
    ('3', 'PM', '产品经理', 1, 3, '负责产品规划与设计'),
    ('4', 'SENIOR_DEV', '高级开发工程师', 1, 4, '负责核心功能开发'),
    ('5', 'MID_DEV', '中级开发工程师', 1, 5, '负责功能模块开发'),
    ('6', 'JUNIOR_DEV', '初级开发工程师', 1, 6, '负责基础功能开发'),
    ('7', 'QA', '测试工程师', 1, 7, '负责系统测试');

-- ----------------------------
-- 用户测试数据
-- ----------------------------
INSERT INTO org_users (id, dept_id, avatar, username, password, nickname, email, phone, description, status)
VALUES
    -- 管理员用户 (密码采用加密存储)
    ('1', '1', 'https://i.pravatar.cc/300?img=1', 'admin', 'JNYRXps5q9kMNuWjksEOWKXfLo+KGmIXzlqQAJyweQpwWXyJEoBqL3B9UYG3cMjA8jphhh1TRRC955aFO3E5P7g3k80b7nbFRDQ2dlkh6UQw3rHQdFU/U7qYeDqCRmuxsXSTQu71VjaXOC3eF44VMvD2EnWSjCOMFJj1tUUVJxVJDxqkI/2eX5XgAcVtXIjspdAjEFW+VvsRl5SSDgXLB7RQtEZo4S8olAekgfjAwmgwK+vGlFvnrTnTSq+Mxm1gAU4iIrow28dXJ4dbeXCxwCoLMGY8+q/g7Vltq+0sm89eUit3Vu24LrSd9BDKjAQjqrzASbrazEi831bwXu8hdA==', '系统管理员', '<EMAIL>', '13800138000', '系统超级管理员账号', 1),
    -- 研发部用户
    ('2', '2', 'https://i.pravatar.cc/300?img=2', 'dev_manager', '$2a$10$JE3j03ZCvWN9b5hXAqGx7OWn/GS23T1W.kYCQGz0GdA.LOYhzRL6m', '研发经理', '<EMAIL>', '13800138001', '研发部门经理', 1),
    ('3', '5', 'https://i.pravatar.cc/300?img=3', 'frontend_dev', '$2a$10$JE3j03ZCvWN9b5hXAqGx7OWn/GS23T1W.kYCQGz0GdA.LOYhzRL6m', '前端开发', '<EMAIL>', '13800138002', '前端开发工程师', 1),
    ('4', '6', 'https://i.pravatar.cc/300?img=4', 'backend_dev', '$2a$10$JE3j03ZCvWN9b5hXAqGx7OWn/GS23T1W.kYCQGz0GdA.LOYhzRL6m', '后端开发', '<EMAIL>', '13800138003', '后端开发工程师', 1),
    ('5', '7', 'https://i.pravatar.cc/300?img=5', 'tester', '$2a$10$JE3j03ZCvWN9b5hXAqGx7OWn/GS23T1W.kYCQGz0GdA.LOYhzRL6m', '测试工程师', '<EMAIL>', '13800138004', '测试工程师', 1),
    -- 其他部门用户
    ('6', '3', 'https://i.pravatar.cc/300?img=6', 'marketing', '$2a$10$JE3j03ZCvWN9b5hXAqGx7OWn/GS23T1W.kYCQGz0GdA.LOYhzRL6m', '市场专员', '<EMAIL>', '13800138005', '市场部专员', 1),
    ('7', '4', 'https://i.pravatar.cc/300?img=7', 'finance', '$2a$10$JE3j03ZCvWN9b5hXAqGx7OWn/GS23T1W.kYCQGz0GdA.LOYhzRL6m', '财务主管', '<EMAIL>', '13800138006', '财务部主管', 1);

-- ----------------------------
-- 用户岗位关联测试数据
-- ----------------------------
INSERT INTO org_user_positions (id, user_id, position_id, is_primary)
VALUES
    ('1', '1', '2', TRUE),  -- 管理员-董事长
    ('2', '2', '2', TRUE),  -- 研发经理-技术总监
    ('3', '3', '5', TRUE),  -- 前端开发-中级开发工程师
    ('4', '4', '4', TRUE),  -- 后端开发-高级开发工程师
    ('5', '5', '7', TRUE),  -- 测试工程师-测试工程师
    ('6', '6', '3', TRUE),  -- 市场专员-产品经理
    ('7', '7', '3', TRUE);  -- 财务主管-产品经理(借用了产品经理职位)

-- !Downs

-- 清除测试数据
DELETE FROM org_user_positions;
DELETE FROM org_users;
DELETE FROM org_positions;
DELETE FROM org_departments;
