-- Org module tables structure

-- !Ups

-- ----------------------------
-- 角色测试数据
-- ----------------------------
INSERT INTO auth_roles (id, name_, code, description, sort_order, is_default)
VALUES
    ('1', '超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', 1, FALSE),
    ('2', '管理员', 'ADMIN', '拥有系统大部分权限', 2, FALSE),
    ('3', '普通用户', 'USER', '普通用户角色', 3, TRUE),
    ('4', '访客', 'GUEST', '访客角色，权限受限', 4, FALSE),
    ('5', '开发人员', 'DEVELOPER', '开发人员，用于测试系统', 5, FALSE);

-- ----------------------------
-- 权限测试数据
-- ----------------------------
INSERT INTO auth_permissions (id, name_, code, type_, description)
VALUES
-- 用户管理权限
('1', '用户列表查看', 'USER:VIEW', 'menu', '查看用户列表'),
('2', '用户创建', 'USER:CREATE', 'button', '创建新用户'),
('3', '用户编辑', 'USER:EDIT', 'button', '编辑用户信息'),
('4', '用户删除', 'USER:DELETE', 'button', '删除用户'),

-- 角色管理权限
('5', '角色列表查看', 'ROLE:VIEW', 'menu', '查看角色列表'),
('6', '角色创建', 'ROLE:CREATE', 'button', '创建新角色'),
('7', '角色编辑', 'ROLE:EDIT', 'button', '编辑角色信息'),
('8', '角色删除', 'ROLE:DELETE', 'button', '删除角色'),

-- 菜单管理权限
('9', '菜单列表查看', 'MENU:VIEW', 'menu', '查看菜单列表'),
('10', '菜单创建', 'MENU:CREATE', 'button', '创建新菜单'),
('11', '菜单编辑', 'MENU:EDIT', 'button', '编辑菜单信息'),
('12', '菜单删除', 'MENU:DELETE', 'button', '删除菜单'),

-- 部门管理权限
('13', '部门列表查看', 'DEPT:VIEW', 'menu', '查看部门列表'),
('14', '部门创建', 'DEPT:CREATE', 'button', '创建新部门'),
('15', '部门编辑', 'DEPT:EDIT', 'button', '编辑部门信息'),
('16', '部门删除', 'DEPT:DELETE', 'button', '删除部门'),

-- 系统监控权限
('17', '在线用户查看', 'MONITOR:ONLINE_USERS', 'menu', '查看在线用户'),
('18', '登录日志查看', 'MONITOR:LOGIN_LOGS', 'menu', '查看登录日志'),
('19', '操作日志查看', 'MONITOR:OPERATION_LOGS', 'menu', '查看操作日志'),
('20', '系统日志查看', 'MONITOR:SYSTEM_LOGS', 'menu', '查看系统日志');

-- ----------------------------
-- 资源测试数据
-- ----------------------------
INSERT INTO auth_resources (id, parent_id, menu_id, name_, code, type_, icon, path_, component, method_, description, permission_code, sort, status, required_auth)
VALUES
-- 系统管理资源
('1', NULL, '2', '系统管理', 'SYSTEM', 'menu', 'ri:settings-3-line', '/system', 'Layout', NULL, '系统管理菜单', 'MENU:VIEW', 1, TRUE, TRUE),
-- 系统管理子资源
('2', '1', '3', '用户管理', 'SYSTEM:USER', 'menu', 'ri:admin-line', '/system/user/index', 'system/user/index', NULL, '用户管理菜单', 'USER:VIEW', 1, TRUE, TRUE),
('3', '1', '4', '角色管理', 'SYSTEM:ROLE', 'menu', 'ri:admin-fill', '/system/role/index', 'system/role/index', NULL, '角色管理菜单', 'ROLE:VIEW', 2, TRUE, TRUE),
('4', '1', '5', '菜单管理', 'SYSTEM:MENU', 'menu', 'ep:menu', '/system/menu/index', 'system/menu/index', NULL, '菜单管理菜单', 'MENU:VIEW', 3, TRUE, TRUE),
('5', '1', '6', '部门管理', 'SYSTEM:DEPT', 'menu', 'ri:git-branch-line', '/system/dept/index', 'system/dept/index', NULL, '部门管理菜单', 'DEPT:VIEW', 4, TRUE, TRUE);

-- 用户管理按钮资源
INSERT INTO auth_resources (id, parent_id, menu_id, name_, code, type_, icon, path_, component, method_, description, permission_code, sort, status, required_auth)
VALUES
    ('6', '2', NULL, '用户添加', 'SYSTEM:USER:ADD', 'button', NULL, NULL, NULL, NULL, '添加用户按钮', 'USER:CREATE', 1, TRUE, TRUE),
    ('7', '2', NULL, '用户编辑', 'SYSTEM:USER:EDIT', 'button', NULL, NULL, NULL, NULL, '编辑用户按钮', 'USER:EDIT', 2, TRUE, TRUE),
    ('8', '2', NULL, '用户删除', 'SYSTEM:USER:DELETE', 'button', NULL, NULL, NULL, NULL, '删除用户按钮', 'USER:DELETE', 3, TRUE, TRUE);

-- 角色管理按钮资源
INSERT INTO auth_resources (id, parent_id, menu_id, name_, code, type_, icon, path_, component, method_, description, permission_code, sort, status, required_auth)
VALUES
    ('9', '3', NULL, '角色添加', 'SYSTEM:ROLE:ADD', 'button', NULL, NULL, NULL, NULL, '添加角色按钮', 'ROLE:CREATE', 1, TRUE, TRUE),
    ('10', '3', NULL, '角色编辑', 'SYSTEM:ROLE:EDIT', 'button', NULL, NULL, NULL, NULL, '编辑角色按钮', 'ROLE:EDIT', 2, TRUE, TRUE),
    ('11', '3', NULL, '角色删除', 'SYSTEM:ROLE:DELETE', 'button', NULL, NULL, NULL, NULL, '删除角色按钮', 'ROLE:DELETE', 3, TRUE, TRUE);

-- API资源
INSERT INTO auth_resources (id, parent_id, menu_id, name_, code, type_, icon, path_, component, method_, description, permission_code, sort, status, required_auth)
VALUES
    ('12', NULL, NULL, '用户查询API', 'API:USER:QUERY', 'api', NULL, '/api/users', NULL, 'GET', '查询用户API', 'USER:VIEW', 1, TRUE, TRUE),
    ('13', NULL, NULL, '用户创建API', 'API:USER:CREATE', 'api', NULL, '/api/users', NULL, 'POST', '创建用户API', 'USER:CREATE', 2, TRUE, TRUE),
    ('14', NULL, NULL, '用户更新API', 'API:USER:UPDATE', 'api', NULL, '/api/users/{id}', NULL, 'PUT', '更新用户API', 'USER:EDIT', 3, TRUE, TRUE),
    ('15', NULL, NULL, '用户删除API', 'API:USER:DELETE', 'api', NULL, '/api/users/{id}', NULL, 'DELETE', '删除用户API', 'USER:DELETE', 4, TRUE, TRUE);

-- ----------------------------
-- 数据权限规则测试数据
-- ----------------------------
INSERT INTO auth_data_permission_rules (id, name_, code, entity, rule_type, rule_config, description)
VALUES
    ('1', '全部数据权限', 'ALL_DATA', 'all', 'all', NULL, '可以访问所有数据'),
    ('2', '部门数据权限', 'DEPT_DATA', 'departments', 'department', '{"includeChildDept": true}', '可以访问本部门及子部门数据'),
    ('3', '本部门数据权限', 'CURRENT_DEPT_DATA', 'departments', 'department', '{"includeChildDept": false}', '只能访问本部门数据'),
    ('4', '自定义数据权限', 'CUSTOM_DATA', 'departments', 'custom', '{"deptIds": ["2", "3", "5"]}', '可以访问指定部门数据'),
    ('5', '仅本人数据权限', 'SELF_DATA', 'users', 'user', NULL, '只能访问本人创建的数据');

-- ----------------------------
-- 用户角色关联测试数据
-- ----------------------------
INSERT INTO auth_user_roles (id, user_id, role_id)
VALUES
    ('1', '1', '1'),  -- 超级管理员用户-超级管理员角色
    ('2', '2', '2'),  -- 研发经理-管理员角色
    ('3', '3', '3'),  -- 前端开发-普通用户角色
    ('4', '4', '3'),  -- 后端开发-普通用户角色
    ('5', '5', '3'),  -- 测试工程师-普通用户角色
    ('6', '6', '3'),  -- 市场专员-普通用户角色
    ('7', '7', '3'),  -- 财务主管-普通用户角色
    ('8', '4', '5');  -- 后端开发-开发人员角色（多角色示例）

-- ----------------------------
-- 角色权限关联测试数据
-- ----------------------------
INSERT INTO auth_role_permissions (id, role_id, permission_id)
VALUES
-- 超级管理员角色-所有权限
('1', '1', '1'),
('2', '1', '2'),
('3', '1', '3'),
('4', '1', '4'),
('5', '1', '5'),
('6', '1', '6'),
('7', '1', '7'),
('8', '1', '8'),
('9', '1', '9'),
('10', '1', '10'),
('11', '1', '11'),
('12', '1', '12'),
('13', '1', '13'),
('14', '1', '14'),
('15', '1', '15'),
('16', '1', '16'),
('17', '1', '17'),
('18', '1', '18'),
('19', '1', '19'),
('20', '1', '20'),

-- 管理员角色-大部分权限
('21', '2', '1'),
('22', '2', '2'),
('23', '2', '3'),
('24', '2', '5'),
('25', '2', '6'),
('26', '2', '7'),
('27', '2', '9'),
('28', '2', '10'),
('29', '2', '11'),
('30', '2', '13'),
('31', '2', '14'),
('32', '2', '15'),
('33', '2', '17'),
('34', '2', '18'),
('35', '2', '19'),

-- 普通用户角色-基本查看权限
('36', '3', '1'),
('37', '3', '5'),
('38', '3', '9'),
('39', '3', '13'),
('40', '3', '17'),

-- 访客角色-最小权限
('41', '4', '1'),
('42', '4', '13'),

-- 开发人员角色-特定权限
('43', '5', '1'),
('44', '5', '2'),
('45', '5', '3'),
('46', '5', '9'),
('47', '5', '10'),
('48', '5', '11'),
('49', '5', '17'),
('50', '5', '18'),
('51', '5', '19'),
('52', '5', '20');

-- ----------------------------
-- 角色数据权限关联测试数据
-- ----------------------------
INSERT INTO auth_role_data_permissions (id, role_id, data_permission_id)
VALUES
    ('1', '1', '1'),  -- 超级管理员-全部数据权限
    ('2', '2', '2'),  -- 管理员-部门数据权限（含子部门）
    ('3', '3', '3'),  -- 普通用户-本部门数据权限
    ('4', '4', '5'),  -- 访客-仅本人数据权限
    ('5', '5', '4');  -- 开发人员-自定义数据权限

-- !Downs

-- 清除测试数据
DELETE FROM auth_role_data_permissions;
DELETE FROM auth_role_permissions;
DELETE FROM auth_user_roles;
DELETE FROM auth_data_permission_rules;
DELETE FROM auth_resources;
DELETE FROM auth_permissions;
DELETE FROM auth_roles;
