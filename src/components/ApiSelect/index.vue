<template>
  <el-select
    :model-value="modelValue"
    v-bind="$attrs"
    :loading="loading"
    @update:model-value="val => emit('update:modelValue', val)"
    @visible-change="handleVisibleChange"
  >
    <el-option
      v-for="item in options"
      :key="item[valueField]"
      :label="item[labelField]"
      :value="item[valueField]"
      :disabled="item.disabled"
    />
    <template v-if="$slots.empty" #empty>
      <slot name="empty" />
    </template>
    <template v-if="$slots.prefix" #prefix>
      <slot name="prefix" />
    </template>
    <template v-if="$slots.default" #default>
      <slot />
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from "vue";
import { http } from "@/utils/http";

const props = defineProps({
  // v-model绑定值
  modelValue: {
    type: [String, Number, Array],
    default: ""
  },
  // API请求地址
  apiUrl: {
    type: String,
    required: true
  },
  // 请求方法，默认为get
  method: {
    type: String,
    default: "get"
  },
  // 请求参数
  params: {
    type: Object,
    default: () => ({})
  },
  // 是否在组件挂载时加载数据
  immediate: {
    type: Boolean,
    default: true
  },
  // 是否在下拉框打开时重新加载数据
  reloadOnOpen: {
    type: Boolean,
    default: false
  },
  // 选项值字段名
  valueField: {
    type: String,
    default: "value"
  },
  // 选项标签字段名
  labelField: {
    type: String,
    default: "label"
  },
  // 响应数据处理函数，用于从API响应中提取选项数组
  responseHandler: {
    type: Function,
    default: (res: any) => {
      if (res.success && res.data) {
        return Array.isArray(res.data) ? res.data : res.data.list || [];
      }
      return [];
    }
  }
});

const emit = defineEmits([
  "update:modelValue",
  "options-loaded",
  "options-load-error"
]);

// 选项数据
const options = ref<any[]>([]);
// 加载状态
const loading = ref(false);
// 标记是否已经进行了初始加载
const initialLoadDone = ref(false);

// 加载选项数据
const loadOptions = async () => {
  if (!props.apiUrl) return;

  loading.value = true;
  try {
    const res = await http.request(
      props.method as any,
      props.apiUrl,
      props.method.toLowerCase() === "get"
        ? { params: props.params }
        : { data: props.params }
    );

    options.value = props.responseHandler(res);
    initialLoadDone.value = true;
    emit("options-loaded", options.value);
  } catch (error) {
    console.error("Failed to load options:", error);
    emit("options-load-error", error);
  } finally {
    loading.value = false;
  }
};

// 处理下拉框可见性变化
const handleVisibleChange = (visible: boolean) => {
  if (visible && props.reloadOnOpen) {
    loadOptions();
  }
};

// 监听参数变化，重新加载数据
watch(
  () => props.params,
  () => {
    loadOptions();
  },
  { deep: true }
);

// 监听API URL变化，重新加载数据
watch(
  () => props.apiUrl,
  () => {
    if (props.apiUrl) {
      loadOptions();
    }
  }
);

// 组件挂载时，如果immediate为true，则立即加载数据
onMounted(() => {
  if (props.immediate && props.apiUrl) {
    loadOptions();
  }
});

// 监听modelValue变化，确保当有值时，对应的选项被高亮
watch(
  () => props.modelValue,
  newVal => {
    // 如果有值但没有选项数据，且未进行过初始加载，尝试加载数据
    if (newVal && options.value.length === 0 && props.apiUrl && !initialLoadDone.value) {
      loadOptions();
    }
  },
  { immediate: true }
);

// 监听options变化，确保当options加载完成后，如果有值，对应的选项被高亮
watch(
  () => options.value,
  newOptions => {
    // 当选项加载完成后，如果有modelValue值，确保对应的选项被高亮
    if (newOptions.length > 0 && props.modelValue) {
      // 这里不需要额外操作，Element Plus的Select组件会自动处理高亮
      // 但是我们可以检查modelValue是否在options中存在
      const hasMatchingOption = newOptions.some(
        option => option[props.valueField] === props.modelValue
      );
      if (!hasMatchingOption) {
        console.warn(
          `Warning: modelValue "${props.modelValue}" does not match any option in the loaded options.`
        );
      }
    }
  }
);
</script>
