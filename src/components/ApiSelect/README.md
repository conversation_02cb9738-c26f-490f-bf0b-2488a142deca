# ApiSelect 组件

ApiSelect 是一个基于 el-select 的封装组件，用于通过 API 加载选项数据。

## 特性

- 支持通过 API 加载选项数据
- 支持自定义请求方法（GET/POST）
- 支持自定义请求参数
- 支持自定义响应数据处理
- 支持在组件挂载时自动加载数据
- 支持在下拉框打开时重新加载数据
- 支持自定义选项值和标签字段名

## 使用方法

```vue
<template>
  <ApiSelect
    v-model="selectedValue"
    placeholder="请选择"
    api-url="customer/source-options"
    :immediate="true"
    :reload-on-open="false"
    @options-loaded="handleOptionsLoaded"
    @options-load-error="handleOptionsLoadError"
  />
</template>

<script setup>
import ApiSelect from "@/components/ApiSelect";
import { ref } from "vue";

const selectedValue = ref("");

const handleOptionsLoaded = (options) => {
  console.log("选项加载成功", options);
};

const handleOptionsLoadError = (error) => {
  console.error("选项加载失败", error);
};
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| modelValue | String/Number/Array | "" | v-model 绑定值 |
| apiUrl | String | - | API 请求地址（必填） |
| method | String | "get" | 请求方法，支持 get 和 post |
| params | Object | {} | 请求参数 |
| immediate | Boolean | true | 是否在组件挂载时加载数据 |
| reloadOnOpen | Boolean | false | 是否在下拉框打开时重新加载数据 |
| valueField | String | "value" | 选项值字段名 |
| labelField | String | "label" | 选项标签字段名 |
| responseHandler | Function | - | 响应数据处理函数，用于从 API 响应中提取选项数组 |

## 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| update:modelValue | 选中值变化时触发 | 选中值 |
| options-loaded | 选项加载成功时触发 | 选项数组 |
| options-load-error | 选项加载失败时触发 | 错误信息 |

## 插槽

ApiSelect 组件支持 el-select 的所有插槽，包括：

- empty：无选项时的内容
- prefix：前缀图标
- default：默认插槽，可用于自定义选项内容

## 注意事项

1. 响应数据格式应为：
```json
{
  "success": true,
  "data": [
    { "label": "选项1", "value": "1" },
    { "label": "选项2", "value": "2" }
  ]
}
```

2. 如果后端返回的数据格式不同，可以通过 `responseHandler` 属性自定义处理函数。

3. 组件内部使用 `:model-value` 和 `@update:model-value` 来实现双向绑定，而不是直接使用 `v-model`，这是因为 Vue 3 不允许在组件内部直接对 props 使用 `v-model`。
