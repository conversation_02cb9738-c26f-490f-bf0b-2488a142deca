import { http } from "@/utils/http";
import { baseUrlApi } from "@/api/utils";

type Result = {
  success: boolean;
  data?: Array<any>;
  message?: string;
};

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    list: Array<any>;
    /** 总条目数 */
    total?: number;
    /** 每页显示条目个数 */
    pageSize?: number;
    /** 当前页数 */
    currentPage?: number;
  };
  message?: string;
};

/** 获取下拉字段 */
export const getSelectOptions = (data?: object) => {
  return http.request<Result>(
    "get",
    baseUrlApi("dict/select-options?dict=" + data),
    {}
  );
};

/** 字典管理-左侧树 */
export const getDictTree = () => {
  return http.request<Result>("get", baseUrlApi("dict/tree"));
};

export const addDictTree = (data?: object) => {
  return http.request<Result>("post", baseUrlApi("dict/tree-add"), { data });
};

export const updateDictTree = (data?: object) => {
  return http.request<Result>("post", baseUrlApi("dict/tree-update"), { data });
};

export const delDictTree = (id?: object) => {
  return http.request<Result>("delete", baseUrlApi(`dict/tree/${id}`));
};

/** 字典管理-根据字典 dictId 查字典详情 */
export const getDictDetail = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi("dict/detail"), { data });
};

export const addDictDetail = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi("dict/detail-add"), {
    data
  });
};

export const updateDictDetail = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi("dict/detail-update"), {
    data
  });
};

export const delDictDetail = (data?: any) => {
  return http.request<ResultTable>(
    "delete",
    baseUrlApi(`dict/detail/${data.id}`)
  );
};
