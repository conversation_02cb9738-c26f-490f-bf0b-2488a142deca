import { http } from "@/utils/http";
import { baseUrlApi } from "@/api/utils";

type Result = {
  success: boolean;
  data?: Array<any>;
  message?: string;
};

// Alias for Result type to maintain compatibility
type r = Result;

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    list: Array<any>;
    /** 总条目数 */
    total?: number;
    /** 每页显示条目个数 */
    pageSize?: number;
    /** 当前页数 */
    currentPage?: number;
  };
  message?: string;
};

/** 获取系统管理-用户管理列表 */
export const getUserList = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi("org/user"), { data });
};

/** 系统管理-用户管理-获取所有角色列表 */
export const getAllRoleList = () => {
  return http.request<Result>("get", baseUrlApi("authority/role-all"));
};

/** 系统管理-用户管理-根据userId，获取对应角色id列表（userId：用户id） */
export const getRoleIds = (data?: object) => {
  return http.request<Result>("post", "authority/list-role-ids", { data });
};

/** 获取系统管理-角色管理列表 */
export const getRoleList = (data?: object) => {
  return http.request<ResultTable>("post", baseUrlApi("authority/role"), {
    data
  });
};

/** 获取系统管理-菜单管理列表 */
export const getMenuList = (data?: object) => {
  return http.request<Result>("post", baseUrlApi("menu"), { data });
};

/** 系统管理-菜单管理-创建菜单 */
export const addMenu = (data: object) => {
  // data移除higherMenuOptions属性
  const { higherMenuOptions, ...cleanedData } = data as any;
  return http.request<Result>("post", baseUrlApi("menu/add"), {
    data: cleanedData
  });
};

/** 系统管理-菜单管理-更新菜单 */
export const updateMenu = (data: object) => {
  // data移除higherMenuOptions属性
  const { higherMenuOptions, ...cleanedData } = data as any;
  return http.request<Result>("post", baseUrlApi("menu/update"), {
    data: cleanedData
  });
};

/** 系统管理-菜单管理-删除菜单 */
export const deleteMenu = (id: number) => {
  return http.request<Result>("delete", baseUrlApi(`menu/${id}`));
};

/** 获取系统管理-部门管理列表 */
export const getDeptList = (data?: object) => {
  return http.request<Result>("post", baseUrlApi("org/dept"), { data });
};

/** 新增部门 */
export const addDept = (data?: object) => {
  return http.request<Result>("post", baseUrlApi("org/dept-add"), { data });
};

/** 更新部门 */
export const updateDept = (data?: object) => {
  // data移除higherDeptOptions属性
  const { higherDeptOptions, ...cleanedData } = data as any;
  return http.request<Result>("post", baseUrlApi("org/dept-update"), {
    data: cleanedData
  });
};

/** 删除部门 */
export const deleteDept = (id: number) => {
  return http.request<Result>("delete", baseUrlApi(`org/dept/${id}`));
};

/** 获取系统监控-在线用户列表 */
export const getOnlineLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/online-logs", { data });
};

/** 获取系统监控-登录日志列表 */
export const getLoginLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/login-logs", { data });
};

/** 获取系统监控-操作日志列表 */
export const getOperationLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/operation-logs", { data });
};

/** 获取系统监控-系统日志列表 */
export const getSystemLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/system-logs", { data });
};

/** 获取系统监控-系统日志-根据 id 查日志详情 */
export const getSystemLogsDetail = (data?: object) => {
  return http.request<Result>("post", "/system-logs-detail", { data });
};

/** 获取角色管理-权限-菜单权限 */
export const getRoleMenu = (data?: object) => {
  return http.request<Result>("get", baseUrlApi("menu/role-menu"), { data });
};

/** 获取角色管理-权限-菜单权限-根据角色 id 查对应菜单 */
export const getRoleMenuIds = (data?: any) => {
  return http.request<Result>(
    "get",
    baseUrlApi(`authority/role-menu-ids?id=${data.id}`),
    {}
  );
};

/** 获取租户管理-租户列表 */
export const getTenantList = (data?: object) => {
  return http.request<ResultTable>("post", "/tenant-list", { data });
};

/** 获取租户管理-租户套餐列表 */
export const getTenantPackage = (data?: object) => {
  return http.request<ResultTable>("post", "/tenant-package", { data });
};

/** 获取租户套餐-权限-菜单权限 */
export const getTenantPackageMenu = (data?: object) => {
  return http.request<Result>("post", "/tenant-package-menu", { data });
};

/** 获取租户套餐-权限-菜单权限-根据角色 id 查对应菜单 */
export const getTenantPackageMenuIds = (data?: object) => {
  return http.request<Result>("post", "/tenant-package-menu-ids", { data });
};

/** 获取租户套餐列表（用于下拉选择） */
export const getTenantPackageSimple = () => {
  return http.request<Result>("get", "/tenant-package-simple");
};

/** 系统管理-角色管理-创建角色 */
export const addRole = (data: object) => {
  // 将data的remark 转为 description
  const { remark, ...rest } = data as any;
  return http.request<Result>("post", baseUrlApi("authority/role-add"), {
    data: { ...rest, description: remark }
  });
};

/** 系统管理-角色管理-更新角色 */
export const updateRole = (data: object) => {
  // 将data的remark 转为 description
  const { remark, ...rest } = data as any;
  return http.request<Result>("post", baseUrlApi("authority/role-update"), {
    data: { ...rest, description: remark }
  });
};

/** 系统管理-用户管理-更新角色 */
export const roles2User = (data: object) => {
  return http.request<Result>("post", baseUrlApi("authority/roles2user"), {
    data
  });
};

/** 系统管理-用户管理-更新角色 */
export const userRoles = (data: object) => {
  return http.request<Result>(
    "get",
    baseUrlApi(`authority/user-roles/${data}`),
    {}
  );
};

/** 系统管理-用户管理-重置密码 */
export const resetPassword = (data: object) => {
  return http.request<Result>("post", baseUrlApi(`org/user-reset-pwd`), {
    data
  });
};

/** 系统管理-角色管理-删除角色 */
export const deleteRole = (id: number) => {
  return http.request<Result>("delete", baseUrlApi(`authority/role/${id}`));
};

/** 系统管理-角色管理-保存角色菜单权限 */
export const saveRoleMenu = (data: object) => {
  return http.request<Result>("post", baseUrlApi("authority/role-menu-save"), {
    data
  });
};

/** 系统管理-用户管理-新增用户 */
export const addUser = (data: object) => {
  // data移除higherDeptOptions属性
  const { higherDeptOptions, ...cleanedData } = data as any;
  return http.request<Result>("post", baseUrlApi("org/user-add"), {
    data: cleanedData
  });
};

/** 系统管理-用户管理-更新用户 */
export const updateUser = (data: object) => {
  // data移除higherDeptOptions属性
  const { higherDeptOptions, password, ...cleanedData } = data as any;
  return http.request<Result>("post", baseUrlApi("org/user-update"), {
    data: cleanedData
  });
};

/** 系统管理-用户管理-删除用户 */
export const deleteUser = (id: number) => {
  return http.request<Result>("delete", baseUrlApi(`org/user/${id}`));
};

/** 系统管理-用户管理-批量删除用户 */
export const batchDeleteUser = (ids: Array<number>) => {
  return http.request<r>("delete", baseUrlApi("org/user-batch"), {
    data: { ids }
  });
};

/** 更新用户头像 */
export const updateUserAvatar = (data: FormData) => {
  return http.request<r>("post", baseUrlApi("org/user-avata"), {
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
