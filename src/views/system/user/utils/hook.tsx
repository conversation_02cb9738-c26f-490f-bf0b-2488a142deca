import "./reset.css";
import dayjs from "dayjs";
import roleForm from "../form/role.vue";
import editForm from "../form/index.vue";
import { zxcvbn } from "@zxcvbn-ts/core";
import { handleTree } from "@/utils/tree";
import { message } from "@/utils/message";
import userAvatar from "@/assets/user.jpg";
import { usePublicHooks } from "../../hooks";
import { addDialog } from "@/components/ReDialog";
import type { PaginationProps } from "@pureadmin/table";
import ReCropperPreview from "@/components/ReCropperPreview";
import type { FormItemProps, RoleFormItemProps } from "../utils/types";
import { encrypt } from "@/utils/password";
import {
  getKeyList,
  isAllEmpty,
  hideTextAtIndex,
  deviceDetection
} from "@pureadmin/utils";
import {
  getRoleIds,
  getDeptList,
  getUserList,
  getAllRoleList,
  addUser,
  updateUser,
  deleteUser,
  batchDeleteUser,
  roles2User,
  resetPassword,
  userRoles,
  updateUserAvatar
} from "@/api/system";
import {
  ElForm,
  ElInput,
  ElFormItem,
  ElProgress,
  ElMessageBox
} from "element-plus";
import {
  type Ref,
  h,
  ref,
  toRaw,
  watch,
  computed,
  reactive,
  onMounted,
  Fragment
} from "vue";

export function useUser(tableRef: Ref, treeRef: Ref) {
  const form = reactive({
    // 左侧部门树的id
    deptId: "",
    username: "",
    phone: "",
    status: ""
  });
  const formRef = ref();
  const ruleFormRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  // 上传头像信息
  const avatarInfo = ref();
  const switchLoadMap = ref({});
  const { switchStyle } = usePublicHooks();
  const higherDeptOptions = ref();
  const treeData = ref([]);
  const treeLoading = ref(true);
  const selectedNum = ref(0);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns: TableColumnList = [
    {
      label: "勾选列", // 如果需要表格多选，此处label必须设置
      type: "selection",
      fixed: "left",
      reserveSelection: true // 数据刷新后保留选项
    },
    // {
    //   label: "用户编号",
    //   prop: "id",
    //   width: 90
    // },
    {
      label: "用户头像",
      prop: "avatar",
      cellRenderer: ({ row }) => (
        <el-image
          fit="cover"
          preview-teleported={true}
          src={row.avatar || userAvatar}
          preview-src-list={Array.of(row.avatar || userAvatar)}
          class="w-[24px] h-[24px] rounded-full align-middle"
        />
      ),
      width: 90
    },
    {
      label: "用户名称",
      prop: "username",
      minWidth: 130
    },
    {
      label: "用户昵称",
      prop: "nickname",
      minWidth: 130
    },
    {
      label: "性别",
      prop: "sex",
      minWidth: 90,
      cellRenderer: ({ row, props }) => (
        <el-tag
          size={props.size}
          type={row.sex === 1 ? "danger" : null}
          effect="plain"
        >
          {row.sex === 1 ? "男" : "女"}
        </el-tag>
      )
    },
    {
      label: "部门",
      prop: "dept.name",
      minWidth: 90
    },
    {
      label: "手机号码",
      prop: "phone",
      minWidth: 90,
      formatter: ({ phone }) => hideTextAtIndex(phone, { start: 3, end: 6 })
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 90,
      cellRenderer: scope => (
        <el-switch
          size={scope.props.size === "small" ? "small" : "default"}
          loading={switchLoadMap.value[scope.index]?.loading}
          v-model={scope.row.status}
          active-value={1}
          inactive-value={0}
          active-text="已启用"
          inactive-text="已停用"
          inline-prompt
          style={switchStyle.value}
          onChange={() => onChange(scope as any)}
        />
      )
    },
    {
      label: "创建时间",
      minWidth: 90,
      prop: "createTime",
      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];
  const buttonClass = computed(() => {
    return [
      "h-[20px]!",
      "reset-margin",
      "text-gray-500!",
      "dark:text-white!",
      "dark:hover:text-primary!"
    ];
  });
  // 重置的新密码
  const pwdForm = reactive({
    newPwd: ""
  });
  const pwdProgress = [
    { color: "#e74242", text: "非常弱" },
    { color: "#EFBD47", text: "弱" },
    { color: "#ffa500", text: "一般" },
    { color: "#1bbf1b", text: "强" },
    { color: "#008000", text: "非常强" }
  ];
  // 当前密码强度（0-4）
  const curScore = ref();
  const roleOptions = ref([]);

  function onChange({ row, index }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.username
      }</strong>用户吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[index] = Object.assign(
          {},
          switchLoadMap.value[index],
          {
            loading: true
          }
        );
        // 调用更新用户API来更新状态
        updateUser({
          id: row.id,
          status: row.status
        })
          .then(res => {
            switchLoadMap.value[index] = Object.assign(
              {},
              switchLoadMap.value[index],
              {
                loading: false
              }
            );
            if (res.success) {
              message("已成功修改用户状态", {
                type: "success"
              });
            } else {
              message(`修改用户状态失败: ${res.message}`, {
                type: "error"
              });
              // 如果失败，回滚状态
              row.status === 0 ? (row.status = 1) : (row.status = 0);
            }
          })
          .catch(() => {
            switchLoadMap.value[index] = Object.assign(
              {},
              switchLoadMap.value[index],
              {
                loading: false
              }
            );
            // 如果请求失败，回滚状态
            row.status === 0 ? (row.status = 1) : (row.status = 0);
            message("修改用户状态失败", {
              type: "error"
            });
          });
      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }

  function handleUpdate(row) {
    openDialog("编辑", row);
  }

  function handleDelete(row) {
    console.log("Delete row:", row); // For debugging purpose
    ElMessageBox.confirm(
      `确认要删除用户编号为${row.id}的这条数据吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true
      }
    )
      .then(() => {
        deleteUser(row.id).then(res => {
          if (res.success) {
            message("删除成功", { type: "success" });
            onSearch();
          } else {
            message(`删除失败: ${res.message}`, { type: "error" });
          }
        });
      })
      .catch(() => {});
  }

  function handleSizeChange(val: number) {
    console.log(`${val} items per page`);
  }

  function handleCurrentChange(val: number) {
    console.log(`current page: ${val}`);
  }

  /** 当CheckBox选择项发生变化时会触发该事件 */
  function handleSelectionChange(val) {
    selectedNum.value = val.length;
    // 重置表格高度
    tableRef.value.setAdaptive();
  }

  /** 取消选择 */
  function onSelectionCancel() {
    selectedNum.value = 0;
    // 用于多选表格，清空用户的选择
    tableRef.value.getTableRef().clearSelection();
  }

  /** 批量删除 */
  function onbatchDel() {
    // 返回当前选中的行
    const curSelected = tableRef.value.getTableRef().getSelectionRows();
    const ids = getKeyList(curSelected, "id");

    if (ids.length === 0) {
      message("请选择要删除的数据", { type: "warning" });
      return;
    }

    ElMessageBox.confirm(`确认要批量删除这些数据吗?`, "系统提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      draggable: true
    })
      .then(() => {
        batchDeleteUser(ids).then(res => {
          if (res.success) {
            message("批量删除成功", { type: "success" });
            tableRef.value.getTableRef().clearSelection();
            onSearch();
          } else {
            message(`批量删除失败: ${res.message}`, { type: "error" });
          }
        });
      })
      .catch(() => {});
  }

  async function onSearch() {
    loading.value = true;
    const { data } = await getUserList(toRaw(form));
    dataList.value = data.list;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.deptId = "";
    treeRef.value.onTreeReset();
    onSearch();
  };

  function onTreeSelect({ id, selected }) {
    form.deptId = selected ? id : "";
    onSearch();
  }

  function formatHigherDeptOptions(treeList) {
    // 根据返回数据的status字段值判断追加是否禁用disabled字段，返回处理后的树结构，用于上级部门级联选择器的展示（实际开发中也是如此，不可能前端需要的每个字段后端都会返回，这时需要前端自行根据后端返回的某些字段做逻辑处理）
    if (!treeList || !treeList.length) return;
    const newTreeList = [];
    for (let i = 0; i < treeList.length; i++) {
      treeList[i].disabled = treeList[i].status === 0 ? true : false;
      formatHigherDeptOptions(treeList[i].children);
      newTreeList.push(treeList[i]);
    }
    return newTreeList;
  }

  function openDialog(title = "新增", row?: any) {
    console.log("Opening dialog for", title, "user:", row); // For debugging purpose

    addDialog({
      title: `${title}用户`,
      props: {
        formInline: {
          id: row?.id,
          title,
          higherDeptOptions: formatHigherDeptOptions(higherDeptOptions.value),
          parentId: row?.dept?.id ?? 0,
          deptId: row?.dept?.id ?? 0,
          nickname: row?.nickname ?? "",
          username: row?.username ?? "",
          password: row?.password ?? "",
          phone: row?.phone ?? "",
          email: row?.email ?? "",
          sex: row?.sex ?? "",
          status: row?.status ?? 1,
          remark: row?.remark ?? ""
        }
      },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef, formInline: null }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;

        console.log("Form data before submission:", curData); // For debugging purpose

        FormRef.validate(valid => {
          if (valid) {
            if (title === "新增") {
              addUser(curData).then(res => {
                if (res.success) {
                  message(`新增用户成功`, { type: "success" });
                  done(); // 关闭弹框
                  onSearch(); // 刷新表格数据
                } else {
                  message(`新增用户失败: ${res.message}`, { type: "error" });
                }
              });
            } else {
              updateUser(curData).then(res => {
                if (res.success) {
                  message(`更新用户成功`, { type: "success" });
                  done(); // 关闭弹框
                  onSearch(); // 刷新表格数据
                } else {
                  message(`更新用户失败: ${res.message}`, { type: "error" });
                }
              });
            }
          }
        });
      }
    });
  }

  const cropRef = ref();

  /** 上传头像 */
  function handleUpload(row) {
    const imageInput = document.createElement("input");
    imageInput.type = "file";
    imageInput.accept = "image/jpeg,image/png,image/gif";

    // 标记是否已上传新图片
    const isNewImageUploaded = ref(false);
    // 上传的图片URL
    const uploadedImageUrl = ref("");
    // 用于强制重新渲染组件的key
    const cropperKey = ref(Date.now());

    // 处理文件(包括选择或拖拽的文件)
    const handleFile = file => {
      if (!file) return;

      // 验证文件类型
      if (!["image/jpeg", "image/png", "image/gif"].includes(file.type)) {
        message("只能上传JPG/PNG/GIF图片", { type: "warning" });
        return;
      }

      // 验证文件大小
      // const isLt2M = file.size / 1024 / 1024 < 2;
      // if (!isLt2M) {
      //   message('上传头像图片大小不能超过2MB', { type: 'warning' });
      //   return;
      // }

      // 读取文件内容
      const reader = new FileReader();
      reader.onload = e => {
        // 确保读取到了结果
        if (e.target && e.target.result) {
          console.log("读取到图片数据");
          // 设置图片URL
          uploadedImageUrl.value = e.target.result as string;
          isNewImageUploaded.value = true;
          // 更新key以强制重新渲染裁剪组件
          cropperKey.value = Date.now();
          message("图片已上传，请裁剪", { type: "success" });
        }
      };
      reader.readAsDataURL(file);
    };

    // 监听文件选择
    const handleFileSelect = event => {
      const file = event.target.files[0];
      if (file) {
        console.log("选择文件:", file.name);
        handleFile(file);
      }
    };

    // 处理拖拽事件
    const handleDrop = event => {
      event.preventDefault();
      event.stopPropagation();

      const files = event.dataTransfer.files;
      if (files && files.length > 0) {
        console.log("拖拽文件:", files[0].name);
        handleFile(files[0]);
      }
    };

    // 阻止默认拖拽行为
    const handleDragOver = event => {
      event.preventDefault();
      event.stopPropagation();
    };

    // 设置弹窗
    addDialog({
      title: "上传并裁剪头像",
      width: "50%",
      closeOnClickModal: false,
      fullscreen: deviceDetection(),
      contentRenderer: () =>
        h("div", { class: "upload-avatar-container" }, [
          h("div", { class: "mb-4 border p-4 rounded-md" }, [
            h("h3", { class: "font-bold mb-[10px]" }, "第一步：选择图片"),
            h(
              "div",
              {
                class: "el-upload el-upload--text",
                style: {
                  border: "1px dashed #d9d9d9",
                  borderRadius: "6px",
                  top: "5px",
                  cursor: "pointer",
                  position: "relative",
                  overflow: "hidden",
                  width: "100%",
                  height: "120px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center"
                },
                onClick: () => {
                  // 触发文件选择器
                  imageInput.click();
                },
                onDragover: handleDragOver,
                onDrop: handleDrop
              },
              [
                h("div", { class: "el-upload__text" }, [
                  h("i", { class: "el-icon-plus", style: "font-size: 28px;" }),
                  h(
                    "div",
                    { class: "mt-2 text-lg" },
                    "点击或拖拽图片到此处上传"
                  ),
                  h(
                    "div",
                    { class: "text-gray-400 text-sm mt-1" },
                    "支持JPG/PNG/GIF，单张不超过2MB"
                  )
                ])
              ]
            )
          ]),

          isNewImageUploaded.value
            ? h("div", { class: "my-4 text-success" }, [
                h("el-alert", {
                  title: "图片已上传，请在下方进行裁剪",
                  type: "success",
                  showIcon: true,
                  closable: false
                })
              ])
            : null,

          h("div", { class: "border p-4 rounded-md mb-4" }, [
            h("h3", { class: "font-bold mb-[10px]" }, "第二步：裁剪图片"),
            h(
              "div",
              {
                class: "flex",
                style: {
                  top: "5px",
                  cursor: "pointer",
                  position: "relative",
                  overflow: "hidden",
                  width: "100%",
                  display: "flex"
                }
              },
              [
                // 使用key属性强制重新渲染组件
                h(ReCropperPreview, {
                  key: cropperKey.value,
                  ref: cropRef,
                  imgSrc: isNewImageUploaded.value
                    ? uploadedImageUrl.value
                    : row.avatar || userAvatar,
                  onCropper: info => (avatarInfo.value = info)
                })
              ]
            ),
            h(
              "p",
              { class: "mt-2 text-gray-500 text-sm" },
              "可以拖动裁剪框调整位置和大小，右键点击图片可查看更多选项"
            )
          ])
        ]),
      beforeSure: done => {
        if (!avatarInfo.value) {
          message("请先裁剪图片", { type: "warning" });
          return;
        }

        console.log("裁剪后的图片信息：", avatarInfo.value);

        // 创建表单数据
        const formData = new FormData();
        formData.append("userId", row.id);

        // 将base64转为blob
        if (avatarInfo.value.base64) {
          const base64Data = avatarInfo.value.base64.split(",")[1];
          const blob = base64ToBlob(base64Data, "image/jpeg");
          formData.append("file", blob, "avatar.jpg");

          // 调用上传头像的API
          updateUserAvatar(formData)
            .then(res => {
              if (res.success) {
                message("头像上传成功", { type: "success" });
                done();
                onSearch();
              } else {
                message(`头像上传失败: ${res.message}`, { type: "error" });
              }
            })
            .catch(error => {
              console.error("头像上传错误:", error);
              message("头像上传失败", { type: "error" });
            });
        } else {
          message("裁剪图片有误，请重试", { type: "error" });
        }
      },
      closeCallBack: () => {
        if (cropRef.value && cropRef.value.hidePopover) {
          cropRef.value.hidePopover();
        }
      }
    });

    // 添加文件选择事件监听器
    imageInput.addEventListener("change", handleFileSelect);
  }

  // 辅助函数：将base64转为Blob
  function base64ToBlob(base64Data, contentType) {
    contentType = contentType || "";
    const sliceSize = 1024;
    const byteCharacters = atob(base64Data);
    const bytesLength = byteCharacters.length;
    const slicesCount = Math.ceil(bytesLength / sliceSize);
    const byteArrays = new Array(slicesCount);

    for (let sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {
      const begin = sliceIndex * sliceSize;
      const end = Math.min(begin + sliceSize, bytesLength);

      const bytes = new Array(end - begin);
      for (let offset = begin, i = 0; offset < end; ++i, ++offset) {
        bytes[i] = byteCharacters[offset].charCodeAt(0);
      }
      byteArrays[sliceIndex] = new Uint8Array(bytes);
    }
    return new Blob(byteArrays, { type: contentType });
  }

  watch(
    pwdForm,
    ({ newPwd }) =>
      (curScore.value = isAllEmpty(newPwd) ? -1 : zxcvbn(newPwd).score)
  );

  /** 重置密码 */
  function handleReset(row) {
    addDialog({
      title: `重置 ${row.username} 用户的密码`,
      width: "30%",
      draggable: true,
      closeOnClickModal: false,
      fullscreen: deviceDetection(),
      contentRenderer: () => (
        <Fragment>
          <ElForm ref={ruleFormRef} model={pwdForm}>
            <ElFormItem
              prop="newPwd"
              rules={[
                {
                  required: true,
                  message: "请输入新密码",
                  trigger: "blur"
                }
              ]}
            >
              <ElInput
                clearable
                show-password
                type="password"
                v-model={pwdForm.newPwd}
                placeholder="请输入新密码"
              />
            </ElFormItem>
          </ElForm>
          <div class="my-4 flex">
            {pwdProgress.map(({ color, text }, idx) => (
              <div
                class="w-[19vw]"
                style={{ marginLeft: idx !== 0 ? "4px" : 0 }}
              >
                <ElProgress
                  striped
                  striped-flow
                  duration={curScore.value === idx ? 6 : 0}
                  percentage={curScore.value >= idx ? 100 : 0}
                  color={color}
                  stroke-width={10}
                  show-text={false}
                />
                <p
                  class="text-center"
                  style={{ color: curScore.value === idx ? color : "" }}
                >
                  {text}
                </p>
              </div>
            ))}
          </div>
        </Fragment>
      ),
      closeCallBack: () => (pwdForm.newPwd = ""),
      beforeSure: done => {
        ruleFormRef.value.validate(valid => {
          if (valid) {
            // 表单规则校验通过
            message(`已成功重置 ${row.username} 用户的密码`, {
              type: "success"
            });

            resetPassword({
              userId: row.id,
              newPwd: encrypt(pwdForm.newPwd)
            })
              .then(res => {
                if (res.success) {
                  message(`密码重置成功`, { type: "success" });
                  done(); // 关闭弹框
                  onSearch(); // 刷新表格数据
                } else {
                  message(`密码重置失败: ${res.message}`, { type: "error" });
                }
              })
              .catch(error => {
                console.error("密码重置出错:", error);
                message("密码重置失败", { type: "error" });
              });
          }
        });
      }
    });
  }

  /** 分配角色 */
  async function handleRole(row) {
    try {
      // 获取用户已有的角色列表
      const { data: userRoleList } = await userRoles(row.id);
      // 提取角色ID
      const ids = userRoleList;

      addDialog({
        title: `分配 ${row.username} 用户的角色`,
        props: {
          formInline: {
            username: row?.username ?? "",
            nickname: row?.nickname ?? "",
            roleOptions: roleOptions.value ?? [],
            ids
          }
        },
        width: "400px",
        draggable: true,
        fullscreen: deviceDetection(),
        fullscreenIcon: true,
        closeOnClickModal: false,
        contentRenderer: () => h(roleForm),
        beforeSure: (done, { options }) => {
          const curData = options.props.formInline as RoleFormItemProps;
          // 调用API更新用户角色
          roles2User({
            userId: row.id,
            roleIds: curData.ids
          })
            .then(res => {
              if (res.success) {
                message(`已成功更新 ${row.username} 用户的角色`, {
                  type: "success"
                });
                done(); // 关闭弹框
                onSearch(); // 刷新表格数据
              } else {
                message(`更新用户角色失败: ${res.message}`, {
                  type: "error"
                });
              }
            })
            .catch(error => {
              console.error("更新用户角色出错:", error);
              message("更新用户角色失败", {
                type: "error"
              });
            });
        }
      });
    } catch (error) {
      console.error("获取用户角色失败:", error);
      message("获取用户角色失败", { type: "error" });
    }
  }

  onMounted(async () => {
    treeLoading.value = true;
    onSearch();

    // 归属部门
    const { data } = await getDeptList();
    higherDeptOptions.value = handleTree(data);
    treeData.value = handleTree(data);
    treeLoading.value = false;

    // 角色列表
    roleOptions.value = (await getAllRoleList()).data;
  });

  return {
    form,
    loading,
    columns,
    dataList,
    treeData,
    treeLoading,
    selectedNum,
    pagination,
    buttonClass,
    deviceDetection,
    onSearch,
    resetForm,
    onbatchDel,
    openDialog,
    onTreeSelect,
    handleUpdate,
    handleDelete,
    handleUpload,
    handleReset,
    handleRole,
    handleSizeChange,
    onSelectionCancel,
    handleCurrentChange,
    handleSelectionChange
  };
}
