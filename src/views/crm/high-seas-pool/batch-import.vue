<script setup lang="ts">
import { ref } from "vue";
import { message } from "@/utils/message";
import { http } from "@/utils/http";
import { baseUrlApi } from "@/api/utils";
import { createFormData } from "@pureadmin/utils";

import UploadIcon from "~icons/ri/upload-2-line?width=26&height=26";
import DownloadIcon from "~icons/ri/download-2-line?width=20&height=20";
import { batchImport } from "@/api/crm";

const uploadRef = ref();
const fileList = ref([]);

// 文件上传前的验证
const beforeUpload = file => {
  const isExcel =
    file.type === "application/vnd.ms-excel" ||
    file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

  if (!isExcel) {
    message("只能上传Excel文件！", { type: "warning" });
    return false;
  }

  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message("文件大小不能超过5MB！", { type: "warning" });
    return false;
  }

  return true;
};

// 提交表单
const submitUpload = async () => {
  if (fileList.value.length === 0) {
    message("请先选择要上传的文件", { type: "warning" });
    return;
  }
  // 创建FormData对象
  const formData = createFormData({
    file: { raw: fileList.value[0].raw }
  });

  // 调用批量导入API
  batchImport(formData)
    .then(({ success, data }) => {
      if (success) {
        const { imported, failed, total } = data;
        message(
          `导入成功! 共${total}条数据，成功${imported}条，失败${failed}条`,
          { type: "success" }
        );
        // 清空文件列表
        fileList.value = [];
      } else {
        message("客户数据导入失败", { type: "error" });
      }
    })
    .catch(error => {
      console.error("导入异常:", error);
      message(`导入异常，请重试`, { type: "error" });
    });
};

// 下载模板
const downloadTemplate = () => {
  try {
    // 创建下载链接
    const link = document.createElement("a");
    const url = `${import.meta.env.VITE_APP_BASE_URL.replace('/api', '')}/templates/customer-import-template.xlsx`;

    // Log the URL to verify it
    console.log('Download URL:', url);

    link.href = url;
    link.download = "客户导入模板.xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message("模板下载成功", { type: "success" });
  } catch (error) {
    console.error('Error downloading the template:', error);
    message("模板下载失败", { type: "error" });
  }
};


// 文件状态改变时的钩子
const handleChange = (uploadFile, uploadFiles) => {
  fileList.value = uploadFiles;
};

// 移除文件
const handleRemove = (file, fileList) => {
  fileList.value = fileList;
};
</script>

<template>
  <div class="batch-import-container">
    <div class="template-download mb-4">
      <el-button
        type="primary"
        link
        :icon="DownloadIcon"
        @click="downloadTemplate"
      >
        下载导入模板
      </el-button>
      <p class="text-gray-500 text-sm mt-2">
        请先下载模板，按照模板格式填写客户信息，然后上传文件进行批量导入
      </p>
    </div>

    <div class="upload-area">
      <el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :before-upload="beforeUpload"
        :on-change="handleChange"
        :on-remove="handleRemove"
        accept=".xls,.xlsx"
      >
        <div class="el-upload__text">
          <UploadIcon class="m-auto mb-2" />
          <div>将Excel文件拖到此处，或<em>点击上传</em></div>
          <div class="mt-2 text-gray-500 text-sm">
            支持 .xls, .xlsx 格式，文件大小不超过5MB
          </div>
        </div>
      </el-upload>
    </div>

    <div class="mt-4 text-right">
      <el-button type="primary" @click="submitUpload">开始导入</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.batch-import-container {
  padding: 20px;

  .template-download {
    border-bottom: 1px dashed #eee;
    padding-bottom: 15px;
  }

  .upload-area {
    margin-top: 20px;

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    :deep(.el-upload__text) {
      text-align: center;

      em {
        color: var(--el-color-primary);
        font-style: normal;
      }
    }
  }
}
</style>
