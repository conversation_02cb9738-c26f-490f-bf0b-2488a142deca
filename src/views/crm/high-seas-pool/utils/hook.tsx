import dayjs from "dayjs";
import editForm from "../form.vue";
import batchImportForm from "../batch-import.vue";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "@/plugins/i18n";
import { addDialog } from "@/components/ReDialog";
import type { FormItemProps } from "../utils/types";
import type { PaginationProps } from "@pureadmin/table";
import { deviceDetection } from "@pureadmin/utils";

import { h, onMounted, reactive, type Ref, ref, toRaw, watch } from "vue";
import {
  getHighSeasCustomers,
  addCustomer,
  updateCustomer,
  delCustomer,
  followUp
} from "@/api/crm";
import { useUserStoreHook } from "@/store/modules/user";

export function useRole(treeRef: Ref) {
  const form = reactive({
    name: "",
    code: "",
    status: ""
  });
  const curRow = ref();
  const dataList = ref([]);
  const treeIds = ref([]);
  const treeData = ref([]);
  const isShow = ref(false);
  const loading = ref(true);
  const isLinkage = ref(false);
  const treeSearchValue = ref();
  const isExpandAll = ref(false);
  const isSelectAll = ref(false);
  const treeProps = {
    value: "id",
    label: "title",
    children: "children"
  };
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  function handleDelete(row) {
    ElMessageBox.confirm(
      `确认要删除客户名称为<strong style='color:var(--el-color-primary)'>${row.name}</strong>的这条数据吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        const { success, message: msg } = await delCustomer(row.id);
        if (success) {
          message(`删除客户成功：${row.name}`, { type: "success" });
          onSearch();
        } else {
          message(msg || "删除失败", { type: "error" });
        }
      })
      .catch(() => {});
  }

  function handleSizeChange(val: number) {
    console.log(`${val} items per page`);
  }

  function handleCurrentChange(val: number) {
    console.log(`current page: ${val}`);
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      searchParams: form
    };
    const { data } = await getHighSeasCustomers(params);
    dataList.value = data.list;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  /** 高亮当前权限选中行 */
  function rowStyle({ row: { id } }) {
    return {
      cursor: "pointer",
      background: id === curRow.value?.id ? "var(--el-fill-color-light)" : ""
    };
  }

  const onQueryChanged = (query: string) => {
    treeRef.value!.filter(query);
  };

  const filterMethod = (query: string, node) => {
    return transformI18n(node.title)!.includes(query);
  };

  watch(isSelectAll, val => {
    val
      ? treeRef.value.setCheckedKeys(treeIds.value)
      : treeRef.value.setCheckedKeys([]);
  });

  return {
    form,
    isShow,
    curRow,
    loading,
    rowStyle,
    dataList,
    treeData,
    treeProps,
    isLinkage,
    pagination,
    isExpandAll,
    isSelectAll,
    treeSearchValue,
    onSearch,
    resetForm,
    handleDelete,
    filterMethod,
    transformI18n,
    onQueryChanged,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}

export function useCustomer(treeRef: Ref) {
  const form = reactive({
    name: "",
    mobile: "",
    type_: ""
  });
  const curRow = ref();
  const formRef = ref();
  const dataList = ref([]);
  const isShow = ref(false);
  const loading = ref(true);
  const isLinkage = ref(false);
  const treeSearchValue = ref();
  const isExpandAll = ref(false);
  const isSelectAll = ref(false);
  const treeProps = {
    value: "id",
    label: "title",
    children: "children"
  };
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns: TableColumnList = [
    // {
    //   label: "客户编号",
    //   prop: "id"
    // },
    {
      label: "客户公司",
      prop: "name",
      minWidth: 100,
      align: "left"
    },
    {
      label: "联系人",
      prop: "linkMan"
    },
    {
      label: "职位",
      prop: "position"
    },
    {
      label: "手机号",
      prop: "mobile"
    },
    {
      label: "电话",
      prop: "phone"
    },
    {
      label: "邮箱",
      prop: "email"
    },
    {
      label: "客户来源",
      prop: "source",
      align: "left"
    },
    {
      label: "客户类型",
      prop: "type_",
      align: "left"
    },
    {
      label: "录入时间",
      prop: "createdAt",
      minWidth: 100,
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 210,
      slot: "operation"
    }
  ];

  function handleDelete(row) {
    ElMessageBox.confirm(
      `确认要删除客户名称为<strong style='color:var(--el-color-primary)'>${row.name}</strong>的这条数据吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        // 这里需要替换为实际的删除客户API调用
        const { success, message: msg } = await delCustomer(row.id);
        if (success) {
          message(`删除客户成功：${row.name}`, { type: "success" });
          onSearch();
        } else {
          message(msg || "删除失败", { type: "error" });
        }
      })
      .catch(() => {});
  }

  function handleFollow(row) {
    ElMessageBox.confirm(
      `确认跟踪客户<strong style='color:var(--el-color-primary)'>${row.name}</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        // 这里需要替换为实际的跟踪客户API调用

        const userId = useUserStoreHook().id;
        const { success, message: msg } = await followUp({
          salesmanId: userId,
          customerId: row.id
        });
        if (success) {
          message(`跟踪客户成功：${row.name}`, { type: "success" });
          onSearch();
        } else {
          message(msg || "跟踪失败", { type: "error" });
        }
      })
      .catch(() => {});
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1; // 切换每页条数时重置到第一页
    onSearch(); // 重新加载数据
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch(); // 重新加载数据
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;
    // 将分页参数与搜索条件一起传递给API
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      searchParams: form
    };

    // 调用API获取数据
    const { data } = await getHighSeasCustomers(params);
    dataList.value = data.list;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    // 重置分页到第一页
    pagination.currentPage = 1;
    onSearch();
  };

  function openDialog(title = "新增", row?: FormItemProps) {
    addDialog({
      title: `${title}客户`,
      props: {
        formInline: {
          id: row?.id ?? "",
          name: row?.name ?? "",
          linkMan: row?.linkMan ?? "",
          position: row?.position ?? "",
          country: row?.country ?? "",
          address: row?.address ?? "",
          phone: row?.phone ?? "",
          mobile: row?.mobile ?? "",
          website: row?.website ?? "",
          email: row?.email ?? "",
          source: row?.source ?? "",
          type_: row?.type_ ?? "",
          description: row?.description ?? ""
        }
      },
      width: "60%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef, formInline: null }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;

        function chores(success, error) {
          if (success) {
            message(`${title}客户成功：${curData.name}`, { type: "success" });
            done(); // 关闭弹框
            onSearch(); // 刷新表格数据
          } else {
            message(error || `${title}失败`, { type: "error" });
          }
        }

        FormRef.validate(async valid => {
          if (valid) {
            if (title === "新增") {
              // 这里需要替换为实际的新增客户API调用
              const { success, message: error } = await addCustomer(curData);
              chores(success, error);
            } else {
              // 这里需要替换为实际的更新客户API调用
              const { success, message: error } = await updateCustomer(curData);
              chores(success, error);
            }
          }
        });
      }
    });
  }

  function rowStyle({ row: { id } }) {
    if (id === curRow.value?.id && isShow.value) {
      return {
        backgroundColor: "var(--el-color-primary-light-9)"
      };
    }
  }
  const onQueryChanged = (query: string) => {
    treeSearchValue.value = query;
  };

  const filterMethod = (query: string, node) => {
    return node.label.includes(query);
  };

  // 初始化加载
  onMounted(() => {
    onSearch();
  });
  // 打开批量导入对话框
  function openBatchImportDialog() {
    addDialog({
      title: "批量导入客户",
      width: "50%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(batchImportForm),
      beforeSure: done => {
        message("批量导入完成", { type: "success" });
        done(); // 关闭弹框
        onSearch(); // 刷新表格数据
      }
    });
  }

  return {
    form,
    isShow,
    curRow,
    loading,
    columns,
    rowStyle,
    dataList,
    treeProps,
    isLinkage,
    pagination,
    isExpandAll,
    isSelectAll,
    treeSearchValue,
    onSearch,
    resetForm,
    openDialog,
    openBatchImportDialog,
    handleDelete,
    handleFollow,
    filterMethod,
    transformI18n,
    onQueryChanged,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
