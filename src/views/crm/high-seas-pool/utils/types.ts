// 虽然字段很少 但是抽离出来 后续有扩展字段需求就很方便了

interface FormItemProps {
  /** 客户ID */
  id?: string;
  /** 客户名称 */
  name: string;
  /** 联系人 */
  linkMan: string;
  /** 职位 */
  position?: string;
  /** 国家 */
  country?: string;
  /** 地址 */
  address?: string;
  /** 电话 */
  phone?: string;
  /** 手机 */
  mobile?: string;
  /** 网站 */
  website?: string;
  /** 邮箱 */
  email?: string;
  /** 客户来源 */
  source?: string;
  /** 客户类型 */
  type_?: string;
  /** 描述 */
  description?: string;
  /** 角色编号 (保留原有字段) */
  code?: string;
  /** 备注 (保留原有字段) */
  remark?: string;
}

interface FormProps {
  formInline: FormItemProps;
}

export type { FormItemProps, FormProps };
