import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  linkMan: [{ required: true, message: "联系人为必填项", trigger: "blur" }],
  source: [{ required: true, message: "客户来源为必填项", trigger: "change" }],
  type_: [{ required: true, message: "客户类型为必填项", trigger: "change" }],
  mobile: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "手机号码格式不正确",
      trigger: "blur"
    }
  ],
  email: [
    {
      pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
      message: "邮箱格式不正确",
      trigger: "blur"
    }
  ]
});
