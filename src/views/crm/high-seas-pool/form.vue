<script setup lang="ts">
import { ref } from "vue";
import { formRules } from "./utils/rule";
import { FormProps } from "./utils/types";
import ApiSelect from "@/components/ApiSelect";

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    name: "",
    linkMan: "",
    position: "",
    country: "",
    address: "",
    phone: "",
    mobile: "",
    website: "",
    email: "",
    source: "",
    type_: "",
    description: ""
  })
});

const ruleFormRef = ref();
const newFormInline = ref(props.formInline);

function getRef() {
  return ruleFormRef.value;
}

defineExpose({ getRef });
</script>

<template>
  <el-form
    ref="ruleFormRef"
    :model="newFormInline"
    :rules="formRules"
    label-width="82px"
  >
    <el-form-item label="客户公司" prop="name">
      <el-input
        v-model="newFormInline.name"
        clearable
        placeholder="请输入客户公司"
      />
    </el-form-item>
    <el-form-item label="联系人" prop="linkMan">
      <el-input
        v-model="newFormInline.linkMan"
        clearable
        placeholder="请输入联系人"
      />
    </el-form-item>

    <el-form-item label="职位" prop="position">
      <el-input
        v-model="newFormInline.position"
        clearable
        placeholder="请输入职位"
      />
    </el-form-item>

    <el-form-item label="国家" prop="country">
      <el-input
        v-model="newFormInline.country"
        clearable
        placeholder="请输入国家"
      />
    </el-form-item>

    <el-form-item label="地址" prop="address">
      <el-input
        v-model="newFormInline.address"
        clearable
        placeholder="请输入地址"
      />
    </el-form-item>

    <el-form-item label="电话" prop="phone">
      <el-input
        v-model="newFormInline.phone"
        clearable
        placeholder="请输入电话"
      />
    </el-form-item>

    <el-form-item label="手机" prop="mobile">
      <el-input
        v-model="newFormInline.mobile"
        clearable
        placeholder="请输入手机"
      />
    </el-form-item>

    <el-form-item label="网站" prop="website">
      <el-input
        v-model="newFormInline.website"
        clearable
        placeholder="请输入网站"
      />
    </el-form-item>

    <el-form-item label="邮箱" prop="email">
      <el-input
        v-model="newFormInline.email"
        clearable
        placeholder="请输入邮箱"
      />
    </el-form-item>

    <el-form-item label="客户来源" prop="source">
      <ApiSelect
        v-model="newFormInline.source"
        placeholder="请选择客户来源"
        class="w-full"
        api-url="api/dict/select-options?dict=crm-customer-source"
        :immediate="true"
        :reload-on-open="false"
      />
    </el-form-item>

    <el-form-item label="客户类型" prop="type_">
      <ApiSelect
        v-model="newFormInline.type_"
        placeholder="请选择客户类型"
        class="w-full"
        api-url="api/dict/select-options?dict=crm-customer-type"
        :immediate="true"
        :reload-on-open="false"
      />
    </el-form-item>

    <el-form-item label="描述">
      <el-input
        v-model="newFormInline.description"
        placeholder="请输入描述信息"
        type="textarea"
      />
    </el-form-item>
  </el-form>
</template>
