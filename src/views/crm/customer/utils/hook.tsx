import dayjs from "dayjs";
import editForm from "../form.vue";
import { handleTree } from "@/utils/tree";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "@/plugins/i18n";
import { addDialog } from "@/components/ReDialog";
import type { FormItemProps } from "../utils/types";
import type { PaginationProps } from "@pureadmin/table";
import { deviceDetection, getKeyList } from "@pureadmin/utils";
import {
  addRole,
  deleteRole,
  getRoleMenu,
  getRoleMenuIds,
  saveRoleMenu,
  updateRole
} from "@/api/system";

import { getMyCustomers } from "@/api/crm";
import { h, onMounted, reactive, type Ref, ref, toRaw, watch } from "vue";
import { useUserStoreHook } from "@/store/modules/user";

export function useRole(treeRef: Ref) {
  const form = reactive({
    name: "",
    status: "",
    salesmanName: ""
  });
  const curRow = ref();
  const formRef = ref();
  const dataList = ref([]);
  const treeIds = ref([]);
  const treeData = ref([]);
  const isShow = ref(false);
  const loading = ref(true);
  const isLinkage = ref(false);
  const treeSearchValue = ref();
  const switchLoadMap = ref({});
  const isExpandAll = ref(false);
  const isSelectAll = ref(false);
  const treeProps = {
    value: "id",
    label: "title",
    children: "children"
  };
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns: TableColumnList = [
    // {
    //   label: "客户编号",
    //   prop: "id"
    // },
    {
      label: "客户公司",
      prop: "name",
      minWidth: 100,
      align: "left"
    },
    {
      label: "联系人",
      prop: "linkMan"
    },

    {
      label: "职位",
      prop: "position"
    },
    {
      label: "手机号",
      prop: "mobile"
    },
    {
      label: "电话",
      prop: "phone"
    },
    {
      label: "邮箱",
      prop: "email"
    },
    {
      label: "客户来源",
      prop: "source",
      align: "left"
    },
    {
      label: "客户类型",
      prop: "type_",
      align: "left"
    },
    {
      label: "跟进者",
      prop: "salesmanName"
    },
    {
      label: "领取时间",
      prop: "createdAt",
      minWidth: 100,
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 210,
      slot: "operation"
    }
  ];
  // const buttonClass = computed(() => {
  //   return [
  //     "h-[20px]!",
  //     "reset-margin",
  //     "text-gray-500!",
  //     "dark:text-white!",
  //     "dark:hover:text-primary!"
  //   ];
  // });

  function onChange({ row, index }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.name
      }</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        switchLoadMap.value[index] = Object.assign(
          {},
          switchLoadMap.value[index],
          {
            loading: true
          }
        );

        try {
          // 调用更新角色接口
          const { success, message: msg } = await updateRole({
            id: row.id,
            name: row.name,
            code: row.code,
            status: row.status,
            remark: row.remark
          });

          switchLoadMap.value[index] = Object.assign(
            {},
            switchLoadMap.value[index],
            {
              loading: false
            }
          );

          if (success) {
            message(`已${row.status === 0 ? "停用" : "启用"}${row.name}`, {
              type: "success"
            });
            // 刷新列表
            onSearch();
          } else {
            // 如果更新失败，回滚状态并显示错误信息
            row.status === 0 ? (row.status = 1) : (row.status = 0);
            message(msg || "更新状态失败", { type: "error" });
          }
        } catch (error) {
          // 发生异常时，回滚状态并结束加载状态
          row.status === 0 ? (row.status = 1) : (row.status = 0);
          switchLoadMap.value[index] = Object.assign(
            {},
            switchLoadMap.value[index],
            {
              loading: false
            }
          );
          message("更新状态异常", { type: "error" });
        }
      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }

  function handleDelete(row) {
    ElMessageBox.confirm(
      `确认要删除角色名称为<strong style='color:var(--el-color-primary)'>${row.name}</strong>的这条数据吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        const { success, message: msg } = await deleteRole(row.id);
        if (success) {
          message(`删除角色成功：${row.name}`, { type: "success" });
          onSearch();
        } else {
          message(msg || "删除失败", { type: "error" });
        }
      })
      .catch(() => {});
  }

  function handleSizeChange(val: number) {
    console.log(`${val} items per page`);
  }

  function handleCurrentChange(val: number) {
    console.log(`current page: ${val}`);
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;

    const params = {
      userId: useUserStoreHook().id,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      searchParams: form
    };

    const { data } = await getMyCustomers(params);
    dataList.value = data.list;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  function openDialog(title = "新增", row?: FormItemProps) {
    addDialog({
      title: `${title}角色`,
      props: {
        formInline: {
          id: row?.id ?? "",
          name: row?.name ?? "",
          code: row?.code ?? "",
          remark: row?.remark ?? ""
        }
      },
      width: "40%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef, formInline: null }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;
        function chores(success, error) {
          if (success) {
            message(`${title}角色成功：${curData.name}`, { type: "success" });
            done(); // 关闭弹框
            onSearch(); // 刷新表格数据
          } else {
            message(error || `${title}失败`, { type: "error" });
          }
        }
        FormRef.validate(async valid => {
          if (valid) {
            // 表单规则校验通过
            if (title === "新增") {
              // 调用新增接口
              const { success, message: msg } = await addRole(curData);
              chores(success, msg);
            } else {
              // 调用修改接口
              const { success, message: msg } = await updateRole(curData);
              chores(success, msg);
            }
          }
        });
      }
    });
  }

  /** 菜单权限 */
  async function handleMenu(row?: any) {
    const { id } = row;
    if (id) {
      curRow.value = row;
      isShow.value = true;
      const { data } = await getRoleMenuIds({ id });
      treeRef.value.setCheckedKeys(data);
    } else {
      curRow.value = null;
      isShow.value = false;
    }
  }

  /** 高亮当前权限选中行 */
  function rowStyle({ row: { id } }) {
    return {
      cursor: "pointer",
      background: id === curRow.value?.id ? "var(--el-fill-color-light)" : ""
    };
  }

  /** 菜单权限-保存 */
  async function handleSave() {
    const { id, name } = curRow.value;
    const menuIds = treeRef.value.getCheckedKeys();

    // 调用保存菜单权限接口
    const { success, message: msg } = await saveRoleMenu({
      roleId: id,
      menuIds
    });

    if (success) {
      message(`角色名称为${name}的菜单权限修改成功`, {
        type: "success"
      });
    } else {
      message(msg || "保存菜单权限失败", {
        type: "error"
      });
    }
  }

  /** 数据权限 可自行开发 */
  // function handleDatabase() {}

  const onQueryChanged = (query: string) => {
    treeRef.value!.filter(query);
  };

  const filterMethod = (query: string, node) => {
    return transformI18n(node.title)!.includes(query);
  };

  onMounted(async () => {
    onSearch();
    const { data } = await getRoleMenu();
    treeIds.value = getKeyList(data, "id");
    treeData.value = handleTree(data);
  });

  watch(isExpandAll, val => {
    val
      ? treeRef.value.setExpandedKeys(treeIds.value)
      : treeRef.value.setExpandedKeys([]);
  });

  watch(isSelectAll, val => {
    val
      ? treeRef.value.setCheckedKeys(treeIds.value)
      : treeRef.value.setCheckedKeys([]);
  });

  return {
    form,
    isShow,
    curRow,
    loading,
    columns,
    rowStyle,
    dataList,
    treeData,
    treeProps,
    isLinkage,
    pagination,
    isExpandAll,
    isSelectAll,
    treeSearchValue,
    onSearch,
    resetForm,
    openDialog,
    handleMenu,
    handleSave,
    handleDelete,
    filterMethod,
    transformI18n,
    onQueryChanged,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
